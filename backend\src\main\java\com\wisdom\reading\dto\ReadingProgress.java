package com.wisdom.reading.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 阅读进度信息DTO
 * 
 * <AUTHOR> Reading Team
 * @version 1.0.0
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ReadingProgress {
    
    /**
     * 日期
     */
    private String date;
    
    /**
     * 阅读文本数
     */
    private Integer textsRead;
    
    /**
     * 阅读时间（分钟）
     */
    private Long readingTime;
    
    /**
     * 平均进度
     */
    private Double averageProgress;
}
