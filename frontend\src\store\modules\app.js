const state = {
  // 主题
  theme: localStorage.getItem('wisdom-reading-theme') || 'light',

  // 语言
  language: localStorage.getItem('wisdom-reading-language') || 'zh-cn',

  // 页面加载状态
  loading: false
}

const mutations = {
  SET_THEME: (state, theme) => {
    state.theme = theme
    localStorage.setItem('wisdom-reading-theme', theme)
    document.documentElement.setAttribute('data-theme', theme)
  },

  SET_LANGUAGE: (state, language) => {
    state.language = language
    localStorage.setItem('wisdom-reading-language', language)
  },

  SET_LOADING: (state, loading) => {
    state.loading = loading
  }
}

const actions = {
  setTheme({ commit }, theme) {
    commit('SET_THEME', theme)
  },

  setLanguage({ commit }, language) {
    commit('SET_LANGUAGE', language)
  },

  setLoading({ commit }, loading) {
    commit('SET_LOADING', loading)
  }
}

const getters = {
  theme: state => state.theme,
  language: state => state.language,
  loading: state => state.loading
}

export default {
  namespaced: true,
  state,
  mutations,
  actions,
  getters
}
