package com.wisdom.reading.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 系统分析统计DTO
 * 
 * <AUTHOR> Reading Team
 * @version 1.0.0
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class SystemAnalysisStats {
    
    /**
     * 总分析数量
     */
    private Long totalAnalysis;
    
    /**
     * 总用户数
     */
    private Long totalUsers;
    
    /**
     * 今日分析数量
     */
    private Long todayAnalysis;
    
    /**
     * 平均处理时间（毫秒）
     */
    private Double avgProcessingTime;
    
    /**
     * 成功率
     */
    private Double successRate;
}
