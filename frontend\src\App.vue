<template>
  <div id="app">
    <!-- 全局加载状态 -->
    <div v-if="loading" class="global-loading">
      <el-loading-service />
    </div>

    <!-- 主布局 -->
    <el-container class="app-container">
      <!-- 顶部导航栏 -->
      <el-header v-if="showHeader" class="app-header">
        <div class="header-content">
          <!-- Logo和标题 -->
          <div class="logo-section">
            <el-icon class="logo-icon" size="32" color="#409EFF">
              <Reading />
            </el-icon>
            <h1 class="app-title">智慧阅读系统</h1>
          </div>

          <!-- 导航菜单 -->
          <el-menu v-if="isLoggedIn" :default-active="activeMenu" class="header-menu" mode="horizontal" router>
            <el-menu-item index="/">
              <el-icon>
                <House />
              </el-icon>
              <span>首页</span>
            </el-menu-item>
            <el-menu-item index="/text-analysis">
              <el-icon>
                <Document />
              </el-icon>
              <span>文本分析</span>
            </el-menu-item>
            <el-menu-item index="/text-simplification">
              <el-icon>
                <Edit />
              </el-icon>
              <span>文本简化</span>
            </el-menu-item>
            <el-menu-item v-if="isTeacher" index="/teaching-tools">
              <el-icon>
                <Tools />
              </el-icon>
              <span>教学工具</span>
            </el-menu-item>
          </el-menu>

          <!-- 用户信息 -->
          <div v-if="isLoggedIn" class="user-section">
            <el-dropdown @command="handleUserCommand">
              <span class="user-info">
                <el-avatar :size="32" :src="userInfo.avatar">
                  {{ userInfo.realName?.charAt(0) || userInfo.username?.charAt(0) }}
                </el-avatar>
                <span class="username">{{ userInfo.realName || userInfo.username }}</span>
                <el-icon>
                  <ArrowDown />
                </el-icon>
              </span>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item command="profile">
                    <el-icon>
                      <User />
                    </el-icon>
                    个人中心
                  </el-dropdown-item>
                  <el-dropdown-item command="settings">
                    <el-icon>
                      <Setting />
                    </el-icon>
                    设置
                  </el-dropdown-item>
                  <el-dropdown-item divided command="logout">
                    <el-icon>
                      <SwitchButton />
                    </el-icon>
                    退出登录
                  </el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </div>

          <!-- 登录按钮 -->
          <div v-else class="auth-section">
            <el-button @click="$router.push('/auth/login')">登录</el-button>
            <el-button type="primary" @click="$router.push('/auth/register')">注册</el-button>
          </div>
        </div>
      </el-header>

      <!-- 主内容区域 -->
      <el-main class="app-main">
        <router-view />
      </el-main>

      <!-- 底部 -->
      <el-footer v-if="showFooter" class="app-footer">
        <div class="footer-content">
          <p>&copy; 2024 智慧阅读系统. All rights reserved.</p>
          <p>Powered by Wisdom Reading Team</p>
        </div>
      </el-footer>
    </el-container>
  </div>
</template>

<script>
import { computed, onMounted, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useStore } from 'vuex'
import { ElMessage, ElMessageBox } from 'element-plus'
import { getUserInfo, removeToken, removeUserInfo, getToken } from '@/utils/auth'
import {
  Reading,
  House,
  Document,
  Edit,
  Tools,
  User,
  Setting,
  SwitchButton,
  ArrowDown
} from '@element-plus/icons-vue'

export default {
  name: 'App',
  components: {
    Reading,
    House,
    Document,
    Edit,
    Tools,
    User,
    Setting,
    SwitchButton,
    ArrowDown
  },
  setup() {
    const route = useRoute()
    const router = useRouter()
    const store = useStore()

    // 计算属性
    const loading = computed(() => store.getters.loading)
    const userInfo = computed(() => {
      return getUserInfo()
    })
    const isLoggedIn = computed(() => {
      return !!getToken()
    })
    const isTeacher = computed(() => userInfo.value.role === 'TEACHER' || userInfo.value.role === 'ADMIN')

    // 显示控制
    const showHeader = computed(() => {
      const hideHeaderRoutes = ['/auth/login', '/auth/register', '/auth/forgot-password']
      return !hideHeaderRoutes.includes(route.path)
    })

    const showFooter = computed(() => {
      const hideFooterRoutes = ['/auth/login', '/auth/register', '/auth/forgot-password']
      return !hideFooterRoutes.includes(route.path)
    })

    const activeMenu = computed(() => {
      return route.path
    })

    // 用户操作处理
    const handleUserCommand = async (command) => {
      switch (command) {
        case 'profile':
          router.push('/profile')
          break
        case 'settings':
          router.push('/settings')
          break
        case 'logout':
          try {
            await ElMessageBox.confirm(
              '确定要退出登录吗？',
              '提示',
              {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
              }
            )
            // 清除本地存储
            removeToken()
            removeUserInfo()
            ElMessage.success('退出登录成功')
            router.push('/auth/login')
          } catch (error) {
            if (error !== 'cancel') {
              ElMessage.error('退出登录失败')
            }
          }
          break
      }
    }

    // 初始化
    onMounted(async () => {
      // 检查登录状态
      const token = getToken()
      const userInfo = getUserInfo()

      if (token && !userInfo.id) {
        // 如果有token但没有用户信息，尝试获取用户信息
        try {
          // 这里可以调用getUserInfo API
          console.log('需要获取用户信息')
        } catch (error) {
          console.error('获取用户信息失败:', error)
          removeToken()
          removeUserInfo()
        }
      }
    })

    // 路由守卫
    watch(
      () => route.path,
      (newPath) => {
        // 需要登录的页面
        const authRequiredRoutes = [
          '/text-analysis',
          '/text-simplification',
          '/teaching-tools',
          '/profile',
          '/settings'
        ]

        if (authRequiredRoutes.includes(newPath) && !isLoggedIn.value) {
          ElMessage.warning('请先登录')
          router.push('/auth/login')
        }
      },
      { immediate: true }
    )

    return {
      loading,
      isLoggedIn,
      userInfo,
      isTeacher,
      showHeader,
      showFooter,
      activeMenu,
      handleUserCommand
    }
  }
}
</script>

<style lang="scss" scoped>
.app-container {
  min-height: 100vh;
}

.app-header {
  background: #fff;
  border-bottom: 1px solid var(--el-border-color-light);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  padding: 0;
  height: 60px;
  line-height: 60px;
}

.header-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

.logo-section {
  display: flex;
  align-items: center;
  gap: 12px;

  .logo-icon {
    flex-shrink: 0;
  }

  .app-title {
    margin: 0;
    font-size: 20px;
    font-weight: 600;
    color: var(--el-text-color-primary);
    white-space: nowrap;
  }
}

.header-menu {
  flex: 1;
  margin: 0 40px;
  border-bottom: none;

  :deep(.el-menu-item) {
    border-bottom: 2px solid transparent;

    &.is-active {
      border-bottom-color: var(--el-color-primary);
    }
  }
}

.user-section {
  display: flex;
  align-items: center;

  .user-info {
    display: flex;
    align-items: center;
    gap: 8px;
    cursor: pointer;
    padding: 8px 12px;
    border-radius: 6px;
    transition: background-color 0.3s;

    &:hover {
      background-color: var(--el-fill-color-light);
    }

    .username {
      color: var(--el-text-color-primary);
      font-weight: 500;
    }
  }
}

.auth-section {
  display: flex;
  gap: 8px;
}

.app-main {
  padding: 0;
  background-color: var(--el-bg-color-page);
}

.app-footer {
  background: var(--el-bg-color);
  border-top: 1px solid var(--el-border-color-light);
  text-align: center;
  color: var(--el-text-color-regular);

  .footer-content {
    max-width: 1200px;
    margin: 0 auto;

    p {
      margin: 4px 0;
      font-size: 14px;
    }
  }
}

.global-loading {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(255, 255, 255, 0.9);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .header-content {
    padding: 0 16px;
  }

  .logo-section .app-title {
    font-size: 18px;
  }

  .header-menu {
    margin: 0 20px;
  }

  .user-section .username {
    display: none;
  }
}

@media (max-width: 480px) {
  .header-menu {
    display: none;
  }

  .logo-section .app-title {
    font-size: 16px;
  }
}
</style>