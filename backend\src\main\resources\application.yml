server:
  port: 8080
  servlet:
    context-path: /api

spring:
  application:
    name: wisdom-reading-backend
  
  # 数据库配置
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: ***************************************************************************************************************************
    username: root
    password: 123
    
  # Redis配置
  redis:
    host: localhost
    port: 6379
    password: 
    database: 0
    timeout: 3000ms
    lettuce:
      pool:
        max-active: 8
        max-idle: 8
        min-idle: 0
        max-wait: -1ms

  # 文件上传配置
  servlet:
    multipart:
      max-file-size: 10MB
      max-request-size: 10MB

# MyBatis Plus配置
mybatis-plus:
  configuration:
    map-underscore-to-camel-case: true
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
  global-config:
    db-config:
      id-type: auto
      logic-delete-field: deleted
      logic-delete-value: 1
      logic-not-delete-value: 0
  mapper-locations: classpath*:mapper/*.xml

# JWT配置
jwt:
  secret: wisdom-reading-secret-key-2024-very-long-and-secure-key-for-jwt-hmac-sha-algorithm
  expiration: 86400000  # 24小时
  header: Authorization
  prefix: Bearer

# 智慧阅读系统配置
wisdom:
  reading:
    # AI服务配置
    ai:
      text-analysis-url: http://localhost:8081/ai/text-analysis
      text-simplify-url: http://localhost:8081/ai/text-simplify
      text-recommend-url: http://localhost:8081/ai/text-recommend
      timeout: 30000
    
    # 文件存储配置
    file:
      upload-path: ./uploads/
      max-size: 10485760  # 10MB
      allowed-types: txt,doc,docx,pdf
    
    # 分级标准配置
    grade:
      levels:
        - code: PRIMARY_LOW
          name: 小学低年级
          description: 1-2年级
          color: "#67C23A"
        - code: PRIMARY_MID
          name: 小学中年级
          description: 3-4年级
          color: "#E6A23C"
        - code: PRIMARY_HIGH
          name: 小学高年级
          description: 5-6年级
          color: "#F56C6C"
        - code: JUNIOR_HIGH
          name: 初中阶段
          description: 7-9年级
          color: "#909399"
        - code: SENIOR_HIGH
          name: 高中阶段
          description: 10-12年级
          color: "#606266"

# API文档配置
springdoc:
  api-docs:
    path: /v3/api-docs
  swagger-ui:
    path: /swagger-ui.html
    tags-sorter: alpha
    operations-sorter: alpha

# 日志配置
logging:
  level:
    com.wisdom.reading: debug
    org.springframework.security: debug
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
