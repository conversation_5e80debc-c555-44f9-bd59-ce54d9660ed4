import request from '@/utils/request'

/**
 * 用户登录
 * @param {Object} data - 登录数据
 * @param {string} data.username - 用户名
 * @param {string} data.password - 密码
 * @param {boolean} data.rememberMe - 记住我
 * @param {string} data.captcha - 验证码
 * @param {string} data.captchaKey - 验证码key
 */
export function login(data) {
  return request({
    url: '/auth/login',
    method: 'post',
    data
  })
}

/**
 * 用户注册
 * @param {Object} data - 注册数据
 * @param {string} data.username - 用户名
 * @param {string} data.password - 密码
 * @param {string} data.email - 邮箱
 * @param {string} data.realName - 真实姓名
 * @param {string} data.role - 角色
 */
export function register(data) {
  return request({
    url: '/auth/register',
    method: 'post',
    data
  })
}

/**
 * 用户登出
 */
export function logout() {
  return request({
    url: '/auth/logout',
    method: 'post'
  })
}

/**
 * 获取用户信息
 */
export function getUserInfo() {
  return request({
    url: '/auth/user-info',
    method: 'get'
  })
}

/**
 * 刷新Token
 * @param {string} refreshToken - 刷新令牌
 */
export function refreshToken(refreshToken) {
  return request({
    url: '/auth/refresh-token',
    method: 'post',
    params: { refreshToken }
  })
}

/**
 * 修改密码
 * @param {Object} data - 修改密码数据
 * @param {string} data.currentPassword - 当前密码
 * @param {string} data.newPassword - 新密码
 */
export function changePassword(data) {
  return request({
    url: '/auth/change-password',
    method: 'post',
    data
  })
}

/**
 * 忘记密码
 * @param {Object} data - 忘记密码数据
 * @param {string} data.email - 邮箱
 */
export function forgotPassword(data) {
  return request({
    url: '/auth/forgot-password',
    method: 'post',
    data
  })
}

/**
 * 重置密码
 * @param {Object} data - 重置密码数据
 * @param {string} data.token - 重置令牌
 * @param {string} data.newPassword - 新密码
 */
export function resetPassword(data) {
  return request({
    url: '/auth/reset-password',
    method: 'post',
    data
  })
}

/**
 * 发送验证码
 * @param {Object} data - 验证码数据
 * @param {string} data.type - 类型 EMAIL/SMS
 * @param {string} data.target - 邮箱或手机号
 */
export function sendVerificationCode(data) {
  return request({
    url: '/auth/send-verification-code',
    method: 'post',
    data
  })
}

/**
 * 验证邮箱
 * @param {Object} data - 验证数据
 * @param {string} data.email - 邮箱
 * @param {string} data.code - 验证码
 */
export function verifyEmail(data) {
  return request({
    url: '/auth/verify-email',
    method: 'post',
    data
  })
}

/**
 * 更新用户个人信息
 * @param {Object} data - 用户信息数据
 * @param {string} data.realName - 真实姓名
 * @param {string} data.email - 邮箱
 * @param {string} data.phone - 手机号
 * @param {string} data.grade - 年级
 * @param {string} data.school - 学校
 * @param {string} data.className - 班级
 */
export function updateUserProfile(data) {
  return request({
    url: '/auth/update-profile',
    method: 'put',
    data
  })
}

/**
 * 上传用户头像
 * @param {FormData} formData - 包含头像文件的表单数据
 */
export function uploadAvatar(formData) {
  return request({
    url: '/auth/upload-avatar',
    method: 'post',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}
