package com.wisdom.reading.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 分析统计信息DTO
 * 
 * <AUTHOR> Reading Team
 * @version 1.0.0
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class AnalysisStats {
    
    /**
     * 总分析数量
     */
    private Long totalCount;
    
    /**
     * 今日分析数量
     */
    private Long todayCount;
    
    /**
     * 本周分析数量
     */
    private Long weekCount;
    
    /**
     * 本月分析数量
     */
    private Long monthCount;
    
    /**
     * 平均置信度
     */
    private Double avgConfidence;
    
    /**
     * 最常用年级
     */
    private String mostFrequentGrade;
}
