<template>
  <div class="settings-container">
    <div class="settings-header">
      <h1>系统设置</h1>
      <p>个性化您的使用体验</p>
    </div>

    <el-row :gutter="24">
      <el-col :span="16">
        <el-card>
          <template #header>
            <div class="card-header">
              <span>界面设置</span>
            </div>
          </template>

          <el-form label-width="120px" class="settings-form">
            <el-form-item label="主题模式">
              <el-radio-group v-model="settings.theme">
                <el-radio label="light">浅色模式</el-radio>
                <el-radio label="dark">深色模式</el-radio>
                <el-radio label="auto">跟随系统</el-radio>
              </el-radio-group>
            </el-form-item>

            <el-form-item label="语言设置">
              <el-select v-model="settings.language" style="width: 200px">
                <el-option label="简体中文" value="zh-CN" />
                <el-option label="English" value="en-US" />
              </el-select>
            </el-form-item>

            <el-form-item label="字体大小">
              <el-slider v-model="settings.fontSize" :min="12" :max="18" :step="1" show-stops style="width: 200px" />
              <span style="margin-left: 12px">{{ settings.fontSize }}px</span>
            </el-form-item>

            <el-form-item label="动画效果">
              <el-switch v-model="settings.animations" />
            </el-form-item>
          </el-form>
        </el-card>

        <el-card style="margin-top: 20px">
          <template #header>
            <div class="card-header">
              <span>通知设置</span>
            </div>
          </template>

          <el-form label-width="120px" class="settings-form">
            <el-form-item label="邮件通知">
              <el-switch v-model="settings.emailNotifications" />
              <div class="setting-description">
                接收系统重要通知和更新信息
              </div>
            </el-form-item>

            <el-form-item label="浏览器通知">
              <el-switch v-model="settings.browserNotifications" />
              <div class="setting-description">
                在浏览器中显示实时通知
              </div>
            </el-form-item>

            <el-form-item label="声音提醒">
              <el-switch v-model="settings.soundNotifications" />
              <div class="setting-description">
                操作完成时播放提示音
              </div>
            </el-form-item>
          </el-form>
        </el-card>

        <el-card style="margin-top: 20px">
          <template #header>
            <div class="card-header">
              <span>隐私设置</span>
            </div>
          </template>

          <el-form label-width="120px" class="settings-form">
            <el-form-item label="数据收集">
              <el-switch v-model="settings.dataCollection" />
              <div class="setting-description">
                允许收集匿名使用数据以改进产品
              </div>
            </el-form-item>

            <el-form-item label="使用统计">
              <el-switch v-model="settings.usageStats" />
              <div class="setting-description">
                记录使用统计信息用于个人分析
              </div>
            </el-form-item>
          </el-form>
        </el-card>

        <div class="settings-actions">
          <el-button type="primary" @click="saveSettings" :loading="saving">
            保存设置
          </el-button>
          <el-button @click="resetSettings">
            恢复默认
          </el-button>
        </div>
      </el-col>

      <el-col :span="8">
        <el-card>
          <template #header>
            <div class="card-header">
              <span>快捷操作</span>
            </div>
          </template>

          <div class="quick-actions">
            <el-button type="primary" plain @click="clearCache" style="width: 100%; margin-bottom: 12px">
              <el-icon>
                <Delete />
              </el-icon>
              清除缓存
            </el-button>

            <el-button type="warning" plain @click="exportData" style="width: 100%; margin-bottom: 12px">
              <el-icon>
                <Download />
              </el-icon>
              导出数据
            </el-button>

            <el-button type="info" plain @click="checkUpdate" style="width: 100%; margin-bottom: 12px">
              <el-icon>
                <Refresh />
              </el-icon>
              检查更新
            </el-button>
          </div>
        </el-card>

        <el-card style="margin-top: 20px">
          <template #header>
            <div class="card-header">
              <span>系统信息</span>
            </div>
          </template>

          <div class="system-info">
            <div class="info-item">
              <label>版本号：</label>
              <span>v1.0.0</span>
            </div>
            <div class="info-item">
              <label>浏览器：</label>
              <span>{{ browserInfo }}</span>
            </div>
            <div class="info-item">
              <label>最后更新：</label>
              <span>2024-01-01</span>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script>
import { ElMessage, ElMessageBox } from 'element-plus'
import { Delete, Download, Refresh } from '@element-plus/icons-vue'

export default {
  name: 'Settings',
  components: {
    Delete,
    Download,
    Refresh
  },
  data() {
    return {
      saving: false,
      settings: {
        theme: 'light',
        language: 'zh-CN',
        fontSize: 14,
        animations: true,
        emailNotifications: true,
        browserNotifications: false,
        soundNotifications: true,
        dataCollection: false,
        usageStats: true
      }
    }
  },
  computed: {
    browserInfo() {
      const ua = navigator.userAgent
      if (ua.includes('Chrome')) return 'Chrome'
      if (ua.includes('Firefox')) return 'Firefox'
      if (ua.includes('Safari')) return 'Safari'
      if (ua.includes('Edge')) return 'Edge'
      return '未知'
    }
  },
  mounted() {
    this.loadSettings()
  },
  methods: {
    loadSettings() {
      const saved = localStorage.getItem('app-settings')
      if (saved) {
        try {
          this.settings = { ...this.settings, ...JSON.parse(saved) }
        } catch (error) {
          console.warn('加载设置失败:', error)
        }
      }
    },

    async saveSettings() {
      try {
        this.saving = true

        // 保存到本地存储
        localStorage.setItem('app-settings', JSON.stringify(this.settings))

        // TODO: 如果需要，可以调用API保存到服务器

        ElMessage.success('设置保存成功')

        // 应用设置
        this.applySettings()
      } catch (error) {
        console.error('保存设置失败:', error)
        ElMessage.error('保存设置失败')
      } finally {
        this.saving = false
      }
    },

    resetSettings() {
      ElMessageBox.confirm('确定要恢复默认设置吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.settings = {
          theme: 'light',
          language: 'zh-CN',
          fontSize: 14,
          animations: true,
          emailNotifications: true,
          browserNotifications: false,
          soundNotifications: true,
          dataCollection: false,
          usageStats: true
        }
        ElMessage.success('已恢复默认设置')
      }).catch(() => {
        // 用户取消
      })
    },

    applySettings() {
      // 应用字体大小
      document.documentElement.style.fontSize = this.settings.fontSize + 'px'

      // 应用主题（如果有主题切换功能）
      if (this.settings.theme === 'dark') {
        document.documentElement.classList.add('dark')
      } else {
        document.documentElement.classList.remove('dark')
      }
    },

    clearCache() {
      ElMessageBox.confirm('清除缓存将删除所有本地数据，确定继续吗？', '警告', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        // 清除除了用户信息和token之外的缓存
        const keysToKeep = ['token', 'userInfo', 'app-settings']
        const keysToRemove = []

        for (let i = 0; i < localStorage.length; i++) {
          const key = localStorage.key(i)
          if (!keysToKeep.includes(key)) {
            keysToRemove.push(key)
          }
        }

        keysToRemove.forEach(key => localStorage.removeItem(key))

        ElMessage.success('缓存清除成功')
      }).catch(() => {
        // 用户取消
      })
    },

    exportData() {
      try {
        const data = {
          settings: this.settings,
          exportTime: new Date().toISOString(),
          version: '1.0.0'
        }

        const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' })
        const url = URL.createObjectURL(blob)
        const a = document.createElement('a')
        a.href = url
        a.download = `wisdom-reading-settings-${new Date().toISOString().split('T')[0]}.json`
        document.body.appendChild(a)
        a.click()
        document.body.removeChild(a)
        URL.revokeObjectURL(url)

        ElMessage.success('数据导出成功')
      } catch (error) {
        console.error('导出数据失败:', error)
        ElMessage.error('导出数据失败')
      }
    },

    checkUpdate() {
      ElMessage.info('当前已是最新版本')
    }
  }
}
</script>

<style lang="scss" scoped>
.settings-container {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
  min-height: calc(100vh - 120px);
  background-color: var(--el-bg-color-page);
}

.settings-header {
  margin-bottom: 24px;
  text-align: center;

  h1 {
    color: var(--el-text-color-primary);
    margin-bottom: 8px;
  }

  p {
    color: var(--el-text-color-regular);
    margin: 0;
  }
}

.card-header {
  font-weight: 600;
  color: var(--el-text-color-primary);
}

.settings-form {
  .el-form-item {
    margin-bottom: 24px;
  }

  .setting-description {
    font-size: 12px;
    color: var(--el-text-color-secondary);
    margin-top: 4px;
    line-height: 1.4;
  }
}

.settings-actions {
  margin-top: 24px;
  text-align: center;

  .el-button {
    margin: 0 8px;
  }
}

.quick-actions {
  .el-button {
    justify-content: flex-start;

    .el-icon {
      margin-right: 8px;
    }
  }
}

.system-info {
  .info-item {
    display: flex;
    align-items: center;
    margin-bottom: 12px;

    label {
      min-width: 80px;
      color: var(--el-text-color-regular);
      font-size: 14px;
    }

    span {
      color: var(--el-text-color-primary);
      flex: 1;
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .settings-container {
    padding: 16px;
  }

  .el-col {
    margin-bottom: 16px;
  }

  .settings-form {
    :deep(.el-form-item__label) {
      width: 100px !important;
    }
  }
}
</style>
