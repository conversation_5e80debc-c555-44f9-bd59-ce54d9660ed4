package com.wisdom.reading.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 系统简化统计DTO
 * 
 * <AUTHOR> Reading Team
 * @version 1.0.0
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class SystemSimplificationStats {
    
    /**
     * 总简化数量
     */
    private Long totalSimplifications;
    
    /**
     * 总用户数
     */
    private Long totalUsers;
    
    /**
     * 今日简化数量
     */
    private Long todaySimplifications;
    
    /**
     * 平均处理时间（毫秒）
     */
    private Double avgProcessingTime;
    
    /**
     * 成功率
     */
    private Double successRate;
    
    /**
     * 平均质量评分
     */
    private Double avgQualityScore;
}
