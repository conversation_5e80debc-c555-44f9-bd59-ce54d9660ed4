# 用户中心功能说明

## 📋 功能概览

用户中心是智慧阅读系统的个人管理中心，提供完整的用户信息管理和系统设置功能。

## 🎯 主要功能

### 1. 个人信息管理
- **基本信息展示**：用户名、真实姓名、邮箱、手机号、角色等
- **头像管理**：支持上传和更换用户头像
- **角色特定信息**：学生用户显示年级、学校、班级信息
- **账户状态**：显示账户状态、注册时间、最后登录时间

### 2. 资料编辑
- **个人信息修改**：真实姓名、邮箱、手机号等
- **学生信息**：年级、学校、班级（仅学生用户）
- **表单验证**：完整的前端验证规则
- **实时保存**：修改后立即同步到服务器

### 3. 密码管理
- **安全密码修改**：需要验证当前密码
- **密码强度检查**：6-20字符长度限制
- **确认密码验证**：防止输入错误

### 4. 使用统计
- **功能使用统计**：文本分析、简化次数统计
- **教师专属统计**：教学资源生成统计（仅教师用户）
- **活动时间线**：最近使用记录展示

## 🔧 技术实现

### API接口
```javascript
// 获取用户信息
GET /auth/user-info

// 更新用户信息
PUT /auth/update-profile

// 修改密码
POST /auth/change-password

// 上传头像
POST /auth/upload-avatar
```

### 组件结构
```
UserCenter/
├── index.vue          # 用户中心入口
├── Profile.vue        # 个人资料管理
└── README.md         # 功能说明
```

### 路由配置
- `/profile` - 个人中心主页
- `/settings` - 系统设置页面

## 🎨 界面设计

### 布局结构
- **左侧信息卡片**：个人信息展示区域
- **右侧功能区域**：标签页形式的功能模块
- **响应式设计**：支持移动端和桌面端

### 功能标签页
1. **编辑资料**：个人信息修改表单
2. **修改密码**：密码管理功能
3. **使用统计**：数据统计和活动记录

## 🔒 安全特性

### 数据验证
- 前端表单验证
- 后端API验证
- 密码安全检查

### 权限控制
- 登录状态验证
- 角色权限检查
- 敏感操作确认

## 📱 用户体验

### 交互设计
- 直观的信息展示
- 清晰的操作反馈
- 友好的错误提示

### 响应式适配
- 移动端优化布局
- 触摸友好的交互
- 自适应屏幕尺寸

## 🚀 使用方法

### 访问用户中心
1. 登录系统后，点击右上角用户头像
2. 选择"个人中心"菜单项
3. 或直接访问 `/profile` 路由

### 修改个人信息
1. 在"编辑资料"标签页中修改信息
2. 点击"保存修改"按钮
3. 系统会自动验证并保存数据

### 更换头像
1. 点击头像下方的"更换头像"按钮
2. 选择图片文件（支持JPG、PNG格式，最大2MB）
3. 预览确认后点击"确定"上传

### 修改密码
1. 切换到"修改密码"标签页
2. 输入当前密码和新密码
3. 确认新密码后提交修改

## 🔄 后续扩展

### 计划功能
- [ ] 个人数据导出
- [ ] 账户注销功能
- [ ] 第三方账户绑定
- [ ] 个性化主题设置
- [ ] 消息通知中心

### API扩展
- [ ] 用户活动日志接口
- [ ] 数据统计接口
- [ ] 文件上传接口优化
- [ ] 批量操作接口

## 📝 注意事项

1. **API接口**：部分接口（如更新用户信息、上传头像）需要后端实现
2. **权限验证**：所有操作都需要用户登录状态
3. **数据同步**：修改后的信息会同步到本地存储和全局状态
4. **错误处理**：完善的错误提示和异常处理机制

## 🎯 设计理念

用户中心的设计遵循以下原则：
- **用户友好**：简洁直观的界面设计
- **功能完整**：覆盖用户管理的核心需求
- **安全可靠**：严格的数据验证和权限控制
- **扩展性强**：模块化设计，便于功能扩展
