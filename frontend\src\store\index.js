import { createStore } from 'vuex'
import app from './modules/app'

const store = createStore({
  modules: {
    app
  },

  state: {
    // 全局状态
    loading: false
  },

  mutations: {
    // 全局mutations
    SET_LOADING: (state, loading) => {
      state.loading = loading
    }
  },

  actions: {
    // 全局actions
    setLoading({ commit }, loading) {
      commit('SET_LOADING', loading)
    }
  },

  getters: {
    // 全局getters
    loading: state => state.loading
  }
})

export default store
