package com.wisdom.reading.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 热门文本信息DTO
 * 
 * <AUTHOR> Reading Team
 * @version 1.0.0
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class PopularText {
    
    /**
     * 文本标题
     */
    private String textTitle;
    
    /**
     * 文本年级
     */
    private String textGrade;
    
    /**
     * 阅读次数
     */
    private Integer readCount;
    
    /**
     * 平均进度
     */
    private Double averageProgress;
    
    /**
     * 平均评分
     */
    private Double averageRating;
}
