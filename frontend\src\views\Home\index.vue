<template>
  <div class="home">
    <div class="hero-section">
      <h1>智慧阅读系统</h1>
      <p class="subtitle">AI驱动的文本难度分级与阅读辅助系统</p>

      <div class="quick-actions">
        <el-button type="primary" size="large" @click="$router.push('/text-analysis')">
          <el-icon>
            <Document />
          </el-icon>
          文本分析
        </el-button>
        <el-button size="large" @click="$router.push('/text-simplification')">
          <el-icon>
            <Edit />
          </el-icon>
          文本简化
        </el-button>
        <el-button v-if="isTeacher" size="large" @click="$router.push('/teaching-tools')">
          <el-icon>
            <Tools />
          </el-icon>
          教学工具
        </el-button>
      </div>
    </div>

    <div class="features-section">
      <h2>核心功能</h2>
      <el-row :gutter="24" justify="center">
        <el-col :span="12">
          <el-card class="feature-card" shadow="hover">
            <div class="feature-icon">
              <el-icon size="48" color="#409EFF">
                <DataAnalysis />
              </el-icon>
            </div>
            <h3>智能文本分析</h3>
            <p>运用AI技术深度分析文本结构、词汇难度、语法复杂度等多个维度，提供精准的难度评估。</p>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <div class="stats-section">
      <h2>系统数据</h2>
      <el-row :gutter="24">
        <el-col :span="6">
          <div class="stat-item">
            <div class="stat-number">50万+</div>
            <div class="stat-label">文本库容量</div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="stat-item">
            <div class="stat-number">99.2%</div>
            <div class="stat-label">分析准确率</div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="stat-item">
            <div class="stat-number">10万+</div>
            <div class="stat-label">用户数量</div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="stat-item">
            <div class="stat-number">95%</div>
            <div class="stat-label">用户满意度</div>
          </div>
        </el-col>
      </el-row>
    </div>
  </div>
</template>

<script>
import { Document, DataAnalysis, Edit, Tools } from '@element-plus/icons-vue'

export default {
  name: 'Home',
  components: {
    Document,
    DataAnalysis,
    Edit,
    Tools
  },
  computed: {
    isTeacher() {
      const userInfo = JSON.parse(localStorage.getItem('userInfo') || '{}')
      return userInfo.role === 'TEACHER' || userInfo.role === 'ADMIN'
    }
  }
}
</script>

<style lang="scss" scoped>
.home {
  min-height: 100vh;
}

.hero-section {
  text-align: center;
  padding: 80px 20px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;

  h1 {
    font-size: 3.5rem;
    font-weight: bold;
    margin-bottom: 20px;

    @media (max-width: 768px) {
      font-size: 2.5rem;
    }
  }

  .subtitle {
    font-size: 1.5rem;
    margin-bottom: 40px;
    opacity: 0.9;

    @media (max-width: 768px) {
      font-size: 1.2rem;
    }
  }
}

.quick-actions {
  display: flex;
  gap: 20px;
  justify-content: center;
  flex-wrap: wrap;

  .el-button {
    min-width: 140px;

    .el-icon {
      margin-right: 8px;
    }
  }
}

.features-section {
  padding: 80px 20px;
  max-width: 1200px;
  margin: 0 auto;

  h2 {
    text-align: center;
    font-size: 2.5rem;
    color: var(--el-text-color-primary);
    margin-bottom: 50px;
  }
}

.feature-card {
  text-align: center;
  padding: 30px 20px;
  height: 280px;
  transition: transform 0.3s ease;

  &:hover {
    transform: translateY(-5px);
  }

  .feature-icon {
    margin-bottom: 20px;
  }

  h3 {
    font-size: 1.5rem;
    color: var(--el-text-color-primary);
    margin-bottom: 16px;
  }

  p {
    color: var(--el-text-color-regular);
    line-height: 1.6;
    font-size: 14px;
  }
}

.stats-section {
  padding: 80px 20px;
  background-color: var(--el-fill-color-lighter);

  h2 {
    text-align: center;
    font-size: 2.5rem;
    color: var(--el-text-color-primary);
    margin-bottom: 50px;
  }

  .el-row {
    max-width: 800px;
    margin: 0 auto;
  }
}

.stat-item {
  text-align: center;
  padding: 20px;

  .stat-number {
    font-size: 2.5rem;
    font-weight: bold;
    color: var(--el-color-primary);
    margin-bottom: 8px;
  }

  .stat-label {
    font-size: 1rem;
    color: var(--el-text-color-regular);
  }
}
</style>
