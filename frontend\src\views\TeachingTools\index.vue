<template>
  <div class="teaching-tools">
    <div class="page-header">
      <h1>教学工具</h1>
      <p>基于文本分析结果，生成丰富的教学资源和辅助工具</p>
    </div>

    <!-- 资源生成 -->
    <el-card class="tools-card">
      <template #header>
        <div class="card-header">
          <span>教学资源生成</span>
        </div>
      </template>

      <el-form ref="formRef" :model="form" :rules="formRules" label-width="120px">
        <el-form-item label="分析ID" prop="analysisId">
          <el-input v-model="form.analysisId" placeholder="请输入文本分析ID" type="number" style="width: 200px" />
          <el-button @click="loadAnalysisInfo" style="margin-left: 8px">加载分析信息</el-button>
        </el-form-item>

        <el-form-item v-if="analysisInfo" label="文本信息">
          <div class="analysis-info">
            <p><strong>标题：</strong>{{ analysisInfo.title || '无标题' }}</p>
            <p><strong>分级：</strong>{{ analysisInfo.gradeResult?.gradeName }}</p>
            <p><strong>字数：</strong>{{ analysisInfo.basicStats?.wordCount }}</p>
          </div>
        </el-form-item>

        <el-form-item label="资源类型" prop="resourceTypes">
          <el-checkbox-group v-model="form.resourceTypes">
            <el-checkbox label="VOCABULARY_CARDS">生词卡片</el-checkbox>
            <el-checkbox label="COMPREHENSION_QUESTIONS">理解题目</el-checkbox>
            <el-checkbox label="READING_GUIDE">阅读指导</el-checkbox>
          </el-checkbox-group>
        </el-form-item>

        <el-form-item label="年级水平">
          <el-select v-model="form.gradeLevel" placeholder="请选择年级水平" style="width: 200px">
            <el-option label="小学低年级" value="PRIMARY_LOW" />
            <el-option label="小学中年级" value="PRIMARY_MID" />
            <el-option label="小学高年级" value="PRIMARY_HIGH" />
            <el-option label="初中阶段" value="JUNIOR_HIGH" />
            <el-option label="高中阶段" value="SENIOR_HIGH" />
          </el-select>
        </el-form-item>

        <el-form-item label="学生水平">
          <el-radio-group v-model="form.studentLevel">
            <el-radio label="BEGINNER">初级</el-radio>
            <el-radio label="INTERMEDIATE">中级</el-radio>
            <el-radio label="ADVANCED">高级</el-radio>
          </el-radio-group>
        </el-form-item>

        <el-form-item label="自定义要求">
          <el-input v-model="form.customRequirements" type="textarea" :rows="3" placeholder="请输入自定义要求（可选）"
            maxlength="500" />
        </el-form-item>

        <el-form-item>
          <el-button type="primary" @click="generateResources" :loading="generating">
            生成教学资源
          </el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 推荐文本 -->
    <el-card class="tools-card">
      <template #header>
        <div class="card-header">
          <span>推荐文本</span>
        </div>
      </template>

      <el-form :model="recommendForm" label-width="120px">
        <el-row :gutter="16">
          <el-col :span="8">
            <el-form-item label="年级水平">
              <el-select v-model="recommendForm.gradeLevel" placeholder="请选择年级">
                <el-option label="小学低年级" value="PRIMARY_LOW" />
                <el-option label="小学中年级" value="PRIMARY_MID" />
                <el-option label="小学高年级" value="PRIMARY_HIGH" />
                <el-option label="初中阶段" value="JUNIOR_HIGH" />
                <el-option label="高中阶段" value="SENIOR_HIGH" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="文本分类">
              <el-input v-model="recommendForm.category" placeholder="如：自然、历史" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="文体类型">
              <el-input v-model="recommendForm.textType" placeholder="如：说明文、记叙文" />
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item label="关键词">
          <el-input v-model="recommendForm.keywords" placeholder="请输入关键词，用逗号分隔" />
        </el-form-item>

        <el-form-item>
          <el-button @click="getRecommendations" :loading="recommending">
            获取推荐文本
          </el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 生成的教学资源 -->
    <el-card v-if="teachingResources" class="result-card">
      <template #header>
        <div class="card-header">
          <span>教学资源</span>
          <div class="header-actions">
            <el-button @click="exportResources">导出资源</el-button>
            <el-button @click="shareResources">分享资源</el-button>
          </div>
        </div>
      </template>

      <div class="teaching-resources">
        <!-- 生词卡片 -->
        <div v-if="teachingResources.vocabularyCards?.length" class="resource-section">
          <h3>生词卡片</h3>
          <div class="vocabulary-cards">
            <div v-for="card in teachingResources.vocabularyCards" :key="card.word" class="vocab-card">
              <div class="card-front">
                <div class="word">{{ card.word }}</div>
                <div class="pronunciation">{{ card.pronunciation }}</div>
              </div>
              <div class="card-back">
                <div class="definition">{{ card.definition }}</div>
                <div class="example">{{ card.example }}</div>
                <el-tag :type="getWordDifficultyType(card.difficulty)" size="small">
                  {{ card.difficulty }}
                </el-tag>
              </div>
            </div>
          </div>
        </div>

        <!-- 理解题目 -->
        <div v-if="teachingResources.comprehensionQuestions?.length" class="resource-section">
          <h3>理解题目</h3>
          <div class="questions">
            <div v-for="(question, index) in teachingResources.comprehensionQuestions" :key="index"
              class="question-item">
              <div class="question-header">
                <span class="question-number">{{ index + 1 }}.</span>
                <el-tag :type="getQuestionType(question.type)" size="small">
                  {{ getQuestionTypeLabel(question.type) }}
                </el-tag>
                <el-tag size="small">{{ question.difficulty }}</el-tag>
              </div>
              <div class="question-content">{{ question.question }}</div>
              <div v-if="question.suggestedAnswer" class="suggested-answer">
                <strong>参考答案：</strong>{{ question.suggestedAnswer }}
              </div>
            </div>
          </div>
        </div>

        <!-- 阅读指导 -->
        <div v-if="teachingResources.readingGuide" class="resource-section">
          <h3>阅读指导</h3>
          <div class="reading-guide">
            <div class="guide-section">
              <h4>课前活动</h4>
              <ul>
                <li v-for="activity in teachingResources.readingGuide.preReadingActivities" :key="activity">
                  {{ activity }}
                </li>
              </ul>
            </div>

            <div class="guide-section">
              <h4>阅读策略</h4>
              <ul>
                <li v-for="strategy in teachingResources.readingGuide.readingStrategies" :key="strategy">
                  {{ strategy }}
                </li>
              </ul>
            </div>

            <div class="guide-section">
              <h4>课后活动</h4>
              <ul>
                <li v-for="activity in teachingResources.readingGuide.postReadingActivities" :key="activity">
                  {{ activity }}
                </li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </el-card>

    <!-- 推荐文本结果 -->
    <el-card v-if="recommendedTexts?.length" class="result-card">
      <template #header>
        <div class="card-header">
          <span>推荐文本</span>
        </div>
      </template>

      <div class="recommended-texts">
        <div v-for="text in recommendedTexts" :key="text.id" class="text-item">
          <div class="text-header">
            <h4>{{ text.title }}</h4>
            <div class="text-meta">
              <el-tag size="small">{{ text.gradeLevel }}</el-tag>
              <el-tag size="small" type="info">{{ text.category }}</el-tag>
              <el-tag size="small" type="success">{{ text.textType }}</el-tag>
            </div>
          </div>
          <div class="text-preview">{{ text.preview }}</div>
          <div class="text-actions">
            <el-button size="small" @click="analyzeRecommendedText(text)">分析此文本</el-button>
            <el-button size="small" type="primary" @click="useAsTeachingMaterial(text)">用作教材</el-button>
          </div>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script>
import {
  generateTeachingResources,
  recommendTexts,
  exportTeachingResource,
  shareTeachingResource
} from '@/api/teachingTools'
import { getAnalysisDetail } from '@/api/textAnalysis'
import { ElMessage } from 'element-plus'

export default {
  name: 'TeachingTools',
  data() {
    return {
      generating: false,
      recommending: false,
      analysisInfo: null,
      form: {
        analysisId: null,
        resourceTypes: ['VOCABULARY_CARDS'],
        gradeLevel: 'PRIMARY_MID',
        studentLevel: 'BEGINNER',
        customRequirements: ''
      },
      formRules: {
        analysisId: [
          { required: true, message: '请输入分析ID', trigger: 'blur' }
        ],
        resourceTypes: [
          { required: true, message: '请选择至少一种资源类型', trigger: 'change' }
        ]
      },
      recommendForm: {
        gradeLevel: 'PRIMARY_MID',
        category: '',
        textType: '',
        keywords: '',
        count: 10
      },
      teachingResources: null,
      recommendedTexts: []
    }
  },
  mounted() {
    // 如果从文本分析页面跳转过来，获取分析ID
    if (this.$route.query.analysisId) {
      this.form.analysisId = parseInt(this.$route.query.analysisId)
      this.loadAnalysisInfo()
    }
  },
  methods: {
    async loadAnalysisInfo() {
      if (!this.form.analysisId) return

      try {
        const response = await getAnalysisDetail(this.form.analysisId)
        this.analysisInfo = response

        // 自动设置年级水平
        if (response.gradeResult?.gradeCode) {
          this.form.gradeLevel = response.gradeResult.gradeCode
        }

        ElMessage.success('分析信息加载成功')
      } catch (error) {
        console.error('加载分析信息失败:', error)
        ElMessage.error('加载分析信息失败')
      }
    },

    async generateResources() {
      try {
        const valid = await this.$refs.formRef.validate()
        if (!valid) return

        this.generating = true
        const response = await generateTeachingResources(this.form)

        this.teachingResources = response
        ElMessage.success('教学资源生成成功')
      } catch (error) {
        console.error('生成教学资源失败:', error)
        ElMessage.error(error.message || '生成教学资源失败')
      } finally {
        this.generating = false
      }
    },

    async getRecommendations() {
      try {
        this.recommending = true

        // 处理关键词
        const keywords = this.recommendForm.keywords
          ? this.recommendForm.keywords.split(',').map(k => k.trim()).filter(k => k)
          : []

        const requestData = {
          ...this.recommendForm,
          keywords
        }

        const response = await recommendTexts(requestData)
        this.recommendedTexts = response
        ElMessage.success('推荐文本获取成功')
      } catch (error) {
        console.error('获取推荐文本失败:', error)
        ElMessage.error(error.message || '获取推荐文本失败')
      } finally {
        this.recommending = false
      }
    },

    async exportResources() {
      if (!this.teachingResources?.resourceId) {
        ElMessage.warning('没有可导出的资源')
        return
      }

      try {
        const response = await exportTeachingResource(this.teachingResources.resourceId, { format: 'PDF' })

        // 创建下载链接
        const blob = new Blob([response], { type: 'application/pdf' })
        const link = document.createElement('a')
        link.href = URL.createObjectURL(blob)
        link.download = `教学资源_${new Date().toISOString().slice(0, 10)}.pdf`
        link.click()

        ElMessage.success('资源导出成功')
      } catch (error) {
        console.error('导出资源失败:', error)
        ElMessage.error('导出资源失败')
      }
    },

    shareResources() {
      if (!this.teachingResources?.resourceId) {
        ElMessage.warning('没有可分享的资源')
        return
      }

      // 这里可以实现分享功能
      ElMessage.info('分享功能开发中...')
    },

    analyzeRecommendedText(text) {
      // 跳转到文本分析页面，并填入推荐的文本
      this.$router.push({
        name: 'TextAnalysis',
        query: {
          text: text.content,
          title: text.title
        }
      })
    },

    useAsTeachingMaterial(text) {
      // 将推荐文本用作教学材料
      ElMessage.success(`已选择"${text.title}"作为教学材料`)
    },

    getWordDifficultyType(difficulty) {
      const types = {
        '基础': 'success',
        '中级': 'warning',
        '高级': 'danger'
      }
      return types[difficulty] || 'info'
    },

    getQuestionType(type) {
      const types = {
        'MULTIPLE_CHOICE': 'primary',
        'OPEN_ENDED': 'success',
        'TRUE_FALSE': 'warning'
      }
      return types[type] || 'info'
    },

    getQuestionTypeLabel(type) {
      const labels = {
        'MULTIPLE_CHOICE': '选择题',
        'OPEN_ENDED': '开放题',
        'TRUE_FALSE': '判断题'
      }
      return labels[type] || type
    }
  }
}
</script>

<style lang="scss" scoped>
.teaching-tools {
  padding: 20px;
}

.page-header {
  margin-bottom: 20px;

  h1 {
    color: var(--el-text-color-primary);
    margin-bottom: 8px;
  }

  p {
    color: var(--el-text-color-regular);
    margin: 0;
  }
}

.tools-card,
.result-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;

  .header-actions {
    display: flex;
    gap: 8px;
  }
}

.analysis-info {
  background: var(--el-fill-color-light);
  padding: 12px;
  border-radius: 6px;

  p {
    margin: 4px 0;
    color: var(--el-text-color-regular);
  }
}

.teaching-resources {
  .resource-section {
    margin-bottom: 32px;

    &:last-child {
      margin-bottom: 0;
    }

    h3 {
      color: var(--el-text-color-primary);
      margin-bottom: 16px;
      border-bottom: 2px solid var(--el-border-color-light);
      padding-bottom: 8px;
      font-size: 18px;
    }
  }
}

.vocabulary-cards {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 16px;

  .vocab-card {
    border: 1px solid var(--el-border-color);
    border-radius: 8px;
    overflow: hidden;

    .card-front {
      background: var(--el-color-primary-light-9);
      padding: 16px;
      text-align: center;

      .word {
        font-size: 20px;
        font-weight: bold;
        color: var(--el-color-primary);
        margin-bottom: 8px;
      }

      .pronunciation {
        color: var(--el-text-color-regular);
        font-style: italic;
      }
    }

    .card-back {
      padding: 16px;
      background: white;

      .definition {
        margin-bottom: 8px;
        color: var(--el-text-color-primary);
      }

      .example {
        margin-bottom: 8px;
        color: var(--el-text-color-regular);
        font-style: italic;
      }
    }
  }
}

.questions {
  .question-item {
    margin-bottom: 24px;
    padding: 16px;
    border: 1px solid var(--el-border-color-light);
    border-radius: 8px;

    .question-header {
      display: flex;
      align-items: center;
      gap: 8px;
      margin-bottom: 8px;

      .question-number {
        font-weight: bold;
        color: var(--el-color-primary);
      }
    }

    .question-content {
      margin-bottom: 8px;
      color: var(--el-text-color-primary);
      line-height: 1.6;
    }

    .suggested-answer {
      color: var(--el-text-color-regular);
      font-size: 14px;
      background: var(--el-fill-color-light);
      padding: 8px;
      border-radius: 4px;
    }
  }
}

.reading-guide {
  .guide-section {
    margin-bottom: 20px;

    h4 {
      color: var(--el-text-color-primary);
      margin-bottom: 8px;
      font-size: 16px;
    }

    ul {
      margin: 0;
      padding-left: 20px;

      li {
        margin: 4px 0;
        color: var(--el-text-color-regular);
        line-height: 1.5;
      }
    }
  }
}

.recommended-texts {
  .text-item {
    margin-bottom: 20px;
    padding: 16px;
    border: 1px solid var(--el-border-color-light);
    border-radius: 8px;

    .text-header {
      display: flex;
      justify-content: space-between;
      align-items: flex-start;
      margin-bottom: 8px;

      h4 {
        margin: 0;
        color: var(--el-text-color-primary);
        flex: 1;
      }

      .text-meta {
        display: flex;
        gap: 4px;
      }
    }

    .text-preview {
      color: var(--el-text-color-regular);
      line-height: 1.6;
      margin-bottom: 12px;
    }

    .text-actions {
      display: flex;
      gap: 8px;
    }
  }
}
</style>
