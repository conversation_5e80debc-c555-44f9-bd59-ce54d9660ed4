<template>
  <div class="register-container">
    <div class="register-card">
      <div class="register-header">
        <h2>智慧阅读系统</h2>
        <p>用户注册</p>
      </div>

      <el-form ref="registerFormRef" :model="registerForm" :rules="registerRules" class="register-form">
        <el-form-item prop="username">
          <el-input v-model="registerForm.username" placeholder="请输入用户名" size="large" prefix-icon="User" />
        </el-form-item>

        <el-form-item prop="email">
          <el-input v-model="registerForm.email" placeholder="请输入邮箱地址" size="large" prefix-icon="Message" />
        </el-form-item>

        <el-form-item prop="realName">
          <el-input v-model="registerForm.realName" placeholder="请输入真实姓名" size="large" prefix-icon="Avatar" />
        </el-form-item>

        <el-form-item prop="role">
          <el-select v-model="registerForm.role" placeholder="请选择角色" size="large" style="width: 100%"
            @change="handleRoleChange">
            <el-option label="学生" value="STUDENT" />
            <el-option label="教师" value="TEACHER" />
          </el-select>
        </el-form-item>

        <!-- 学生用户需要选择年级 -->
        <el-form-item v-if="registerForm.role === 'STUDENT'" prop="grade">
          <el-select v-model="registerForm.grade" placeholder="请选择年级" size="large" style="width: 100%">
            <el-option label="一年级" value="GRADE_1" />
            <el-option label="二年级" value="GRADE_2" />
            <el-option label="三年级" value="GRADE_3" />
            <el-option label="四年级" value="GRADE_4" />
            <el-option label="五年级" value="GRADE_5" />
            <el-option label="六年级" value="GRADE_6" />
            <el-option label="七年级" value="GRADE_7" />
            <el-option label="八年级" value="GRADE_8" />
            <el-option label="九年级" value="GRADE_9" />
            <el-option label="高一" value="GRADE_10" />
            <el-option label="高二" value="GRADE_11" />
            <el-option label="高三" value="GRADE_12" />
          </el-select>
        </el-form-item>

        <!-- 可选字段 -->
        <el-form-item prop="phone">
          <el-input v-model="registerForm.phone" placeholder="请输入手机号（可选）" size="large" prefix-icon="Phone" />
        </el-form-item>

        <el-form-item prop="password">
          <el-input v-model="registerForm.password" type="password" placeholder="请输入密码" size="large" prefix-icon="Lock"
            show-password />
        </el-form-item>

        <el-form-item prop="confirmPassword">
          <el-input v-model="registerForm.confirmPassword" type="password" placeholder="请确认密码" size="large"
            prefix-icon="Lock" show-password />
        </el-form-item>

        <el-form-item>
          <el-button type="primary" size="large" class="register-button" :loading="loading" @click="handleRegister">
            注册
          </el-button>
        </el-form-item>

        <el-form-item>
          <div class="login-link">
            已有账号？
            <el-link type="primary" @click="$router.push('/auth/login')">
              立即登录
            </el-link>
          </div>
        </el-form-item>
      </el-form>
    </div>
  </div>
</template>

<script>
import { register } from '@/api/auth'
import { ElMessage } from 'element-plus'

export default {
  name: 'Register',
  data() {
    const validateConfirmPassword = (rule, value, callback) => {
      if (!value) {
        callback(new Error('确认密码不能为空'))
      } else if (value !== this.registerForm.password) {
        callback(new Error('两次输入密码不一致'))
      } else {
        callback()
      }
    }

    return {
      loading: false,
      registerForm: {
        username: '',
        email: '',
        realName: '',
        role: 'STUDENT',
        grade: '',
        phone: '',
        password: '',
        confirmPassword: ''
      },
      registerRules: {
        username: [
          { required: true, message: '请输入用户名', trigger: 'blur' },
          { min: 3, max: 20, message: '用户名长度在 3 到 20 个字符', trigger: 'blur' }
        ],
        email: [
          { required: true, message: '请输入邮箱地址', trigger: 'blur' },
          { type: 'email', message: '请输入正确的邮箱地址', trigger: 'blur' }
        ],
        realName: [
          { required: true, message: '请输入真实姓名', trigger: 'blur' },
          { min: 2, max: 20, message: '姓名长度在 2 到 20 个字符', trigger: 'blur' }
        ],
        role: [
          { required: true, message: '请选择角色', trigger: 'change' }
        ],
        grade: [
          {
            validator: (rule, value, callback) => {
              if (this.registerForm.role === 'STUDENT' && !value) {
                callback(new Error('学生用户必须选择年级'))
              } else {
                callback()
              }
            },
            trigger: 'change'
          }
        ],
        phone: [
          { pattern: /^1[3-9]\d{9}$/, message: '手机号格式不正确', trigger: 'blur' }
        ],
        password: [
          { required: true, message: '请输入密码', trigger: 'blur' },
          { min: 6, max: 20, message: '密码长度在 6 到 20 个字符', trigger: 'blur' }
        ],
        confirmPassword: [
          { required: true, message: '请确认密码', trigger: 'blur' },
          { validator: validateConfirmPassword, trigger: 'blur' }
        ]
      }
    }
  },
  methods: {
    async handleRegister() {
      try {
        const valid = await this.$refs.registerFormRef.validate()
        if (!valid) return

        this.loading = true

        // 发送完整的注册数据，包括confirmPassword
        await register(this.registerForm)

        ElMessage.success('注册成功，请登录')

        // 跳转到登录页
        this.$router.push('/auth/login')

      } catch (error) {
        console.error('注册失败:', error)
        ElMessage.error(error.message || '注册失败，请重试')
      } finally {
        this.loading = false
      }
    },

    handleRoleChange(role) {
      // 当角色改变时，清空年级选择
      if (role !== 'STUDENT') {
        this.registerForm.grade = ''
      }

      // 动态更新验证规则
      this.$nextTick(() => {
        if (this.$refs.registerFormRef) {
          this.$refs.registerFormRef.clearValidate('grade')
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.register-container {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.register-card {
  width: 400px;
  padding: 40px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.register-header {
  text-align: center;
  margin-bottom: 30px;

  h2 {
    color: #303133;
    margin-bottom: 8px;
    font-size: 24px;
  }

  p {
    color: #909399;
    margin: 0;
  }
}

.register-form {
  .register-button {
    width: 100%;
  }

  .login-link {
    text-align: center;
    color: #909399;
  }
}
</style>
