import { createApp } from 'vue';
import App from './App.vue';
import router from './router';
import store from './store';
import ElementPlus from 'element-plus';
import 'element-plus/dist/index.css';
import './styles/index.scss';

const app = createApp(App);
app.use(router);
app.use(store);
app.use(ElementPlus);

// 挂载应用
app.mount('#app');

// 应用加载完成后添加loaded类，移除加载状态
document.addEventListener('DOMContentLoaded', () => {
    const appElement = document.getElementById('app');
    if (appElement) {
        appElement.classList.add('loaded');
    }
});