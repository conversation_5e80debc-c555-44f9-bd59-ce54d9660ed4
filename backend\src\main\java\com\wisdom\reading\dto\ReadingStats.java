package com.wisdom.reading.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 阅读统计信息DTO
 * 
 * <AUTHOR> Reading Team
 * @version 1.0.0
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ReadingStats {
    
    /**
     * 总阅读时间（分钟）
     */
    private Long totalReadingTime;
    
    /**
     * 总阅读文本数
     */
    private Integer totalTextsRead;
    
    /**
     * 完成的文本数
     */
    private Integer completedTexts;
    
    /**
     * 平均阅读进度
     */
    private Double averageProgress;
    
    /**
     * 最喜欢的年级
     */
    private String favoriteGrade;
    
    /**
     * 最喜欢的类别
     */
    private String favoriteCategory;
}
