<!DOCTYPE html>
<html lang="zh-CN">

<head>
  <meta charset="UTF-8" />
  <link rel="icon" type="image/svg+xml" href="/vite.svg" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>智慧阅读系统</title>
  <meta name="description" content="智慧阅读系统 - 提供文本分析、简化和阅读辅助功能" />
  <meta name="keywords" content="智慧阅读,文本分析,文本简化,阅读辅助" />
  <meta name="author" content="智慧阅读团队" />

  <!-- 预加载关键资源 -->
  <link rel="preconnect" href="https://fonts.googleapis.com">
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>

  <!-- 防止FOUC (Flash of Unstyled Content) -->
  <style>
    /* 初始加载样式 */
    #app {
      min-height: 100vh;
      background-color: #f5f7fa;
    }

    /* 加载状态样式 */
    #app>.loading {
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      background-color: #f5f7fa;
      z-index: 9999;
      text-align: center;
      color: #606266;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
    }

    .loading-spinner {
      width: 40px;
      height: 40px;
      border: 4px solid #e4e7ed;
      border-top: 4px solid #409eff;
      border-radius: 50%;
      animation: spin 1s linear infinite;
      margin: 0 auto 16px;
    }

    @keyframes spin {
      0% {
        transform: rotate(0deg);
      }

      100% {
        transform: rotate(360deg);
      }
    }

    /* 应用加载完成后隐藏加载状态 */
    #app.loaded>.loading {
      display: none;
    }

    #app.loaded {
      display: block;
      background-color: transparent;
    }
  </style>
</head>

<body>
  <div id="app">
    <div class="loading">
      <div class="loading-spinner"></div>
      <div>智慧阅读系统加载中...</div>
    </div>
  </div>
  <script type="module" src="/src/main.js"></script>
</body>

</html>