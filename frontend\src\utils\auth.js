import Cookies from 'js-cookie'

const TokenKey = 'wisdom-reading-token'

export function getToken() {
  return Cookies.get(TokenKey)
}

export function setToken(token) {
  return Cookies.set(TokenKey, token, { expires: 7 }) // 7天过期
}

export function removeToken() {
  return Cookies.remove(TokenKey)
}

// 检查是否已登录
export function isLoggedIn() {
  return !!getToken()
}

/**
 * 安全地获取用户信息
 * @returns {Object} 用户信息对象，如果获取失败返回空对象
 */
export function getUserInfo() {
  try {
    const stored = localStorage.getItem('userInfo')
    if (stored && stored !== 'undefined' && stored !== 'null') {
      return JSON.parse(stored)
    }
  } catch (error) {
    console.warn('解析用户信息失败:', error)
    localStorage.removeItem('userInfo')
  }
  return {}
}

/**
 * 设置用户信息
 * @param {Object} userInfo - 用户信息对象
 */
export function setUserInfo(userInfo) {
  if (userInfo && typeof userInfo === 'object') {
    localStorage.setItem('userInfo', JSON.stringify(userInfo))
  }
}

/**
 * 清除用户信息
 */
export function removeUserInfo() {
  localStorage.removeItem('userInfo')
}

// 检查用户角色
export function hasRole(role) {
  const userInfo = getUserInfo()
  return userInfo.role === role
}

// 检查用户权限
export function hasPermission(permission) {
  const userInfo = getUserInfo()
  const permissions = userInfo.permissions || []
  return permissions.includes(permission)
}

// 检查多个角色
export function hasAnyRole(roles) {
  const userInfo = getUserInfo()
  return roles.includes(userInfo.role)
}

// 检查多个权限
export function hasAnyPermission(permissions) {
  const userInfo = getUserInfo()
  const userPermissions = userInfo.permissions || []
  return permissions.some(permission => userPermissions.includes(permission))
}

/**
 * 检查用户是否为教师
 * @returns {boolean}
 */
export function isTeacher() {
  const userInfo = getUserInfo()
  return userInfo.role === 'TEACHER' || userInfo.role === 'ADMIN'
}
