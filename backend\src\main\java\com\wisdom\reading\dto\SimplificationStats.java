package com.wisdom.reading.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 简化统计信息DTO
 * 
 * <AUTHOR> Reading Team
 * @version 1.0.0
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class SimplificationStats {
    
    /**
     * 总简化数量
     */
    private Long totalCount;
    
    /**
     * 今日简化数量
     */
    private Long todayCount;
    
    /**
     * 本周简化数量
     */
    private Long weekCount;
    
    /**
     * 本月简化数量
     */
    private Long monthCount;
    
    /**
     * 平均质量评分
     */
    private Double avgQualityScore;
    
    /**
     * 平均简化比例
     */
    private Double avgSimplificationRatio;
    
    /**
     * 最常用目标年级
     */
    private String mostFrequentTargetGrade;
}
