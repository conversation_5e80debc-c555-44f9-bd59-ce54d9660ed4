import { createRouter, createWebHistory } from 'vue-router'
import { getToken } from '@/utils/auth'

// 路由组件 - 懒加载
const Home = () => import('@/views/Home/index.vue')
const TextAnalysis = () => import('@/views/TextAnalysis/index.vue')
const TextSimplification = () => import('@/views/TextSimplification/index.vue')
const TeachingTools = () => import('@/views/TeachingTools/index.vue')

// 认证相关组件
const Login = () => import('@/views/Auth/Login.vue')
const Register = () => import('@/views/Auth/Register.vue')

const routes = [
  {
    path: '/',
    name: 'Home',
    component: Home,
    meta: { requiresAuth: false }
  },
  {
    path: '/text-analysis',
    name: 'TextAnalysis',
    component: TextAnalysis,
    meta: { requiresAuth: true }
  },
  {
    path: '/text-simplification',
    name: 'TextSimplification',
    component: TextSimplification,
    meta: { requiresAuth: true }
  },
  {
    path: '/teaching-tools',
    name: 'TeachingTools',
    component: TeachingTools,
    meta: { requiresAuth: true, requiresTeacher: true }
  },
  {
    path: '/auth/login',
    name: 'Login',
    component: Login,
    meta: { requiresAuth: false }
  },
  {
    path: '/auth/register',
    name: 'Register',
    component: Register,
    meta: { requiresAuth: false }
  },
  {
    path: '/:pathMatch(.*)*',
    redirect: '/'
  }
]

const router = createRouter({
  history: createWebHistory(),
  routes
})

// 路由守卫
router.beforeEach((to, from, next) => {
  const token = getToken()
  const userInfo = JSON.parse(localStorage.getItem('userInfo') || '{}')

  // 需要认证的路由
  if (to.meta.requiresAuth && !token) {
    next('/auth/login')
    return
  }

  // 需要教师权限的路由
  if (to.meta.requiresTeacher && userInfo.role !== 'TEACHER') {
    next('/')
    return
  }

  // 已登录用户访问登录/注册页面，重定向到首页
  if ((to.name === 'Login' || to.name === 'Register') && token) {
    next('/')
    return
  }

  next()
})

export default router
