package com.wisdom.reading.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 质量分布DTO
 * 
 * <AUTHOR> Reading Team
 * @version 1.0.0
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class QualityDistribution {
    
    /**
     * 质量范围（如：0-20, 21-40, 41-60, 61-80, 81-100）
     */
    private String qualityRange;
    
    /**
     * 该质量范围的数量
     */
    private Long count;
    
    /**
     * 该质量范围的百分比
     */
    private Double percentage;
}
