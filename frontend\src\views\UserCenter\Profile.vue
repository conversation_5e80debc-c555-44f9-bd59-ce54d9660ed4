<template>
  <div class="profile-container">
    <div class="profile-header">
      <h1>个人中心</h1>
      <p>管理您的个人信息和账户设置</p>
    </div>

    <el-row :gutter="24">
      <!-- 左侧个人信息卡片 -->
      <el-col :span="8">
        <el-card class="profile-card">
          <template #header>
            <div class="card-header">
              <span>个人信息</span>
            </div>
          </template>

          <div class="profile-info">
            <!-- 头像 -->
            <div class="avatar-section">
              <el-avatar :size="80" :src="userInfo.avatar" class="user-avatar">
                {{ userInfo.realName?.charAt(0) || userInfo.username?.charAt(0) }}
              </el-avatar>
              <el-button size="small" @click="showAvatarUpload = true" style="margin-top: 8px">
                更换头像
              </el-button>
            </div>

            <!-- 基本信息 -->
            <div class="basic-info">
              <div class="info-item">
                <label>用户名：</label>
                <span>{{ userInfo.username }}</span>
              </div>
              <div class="info-item">
                <label>真实姓名：</label>
                <span>{{ userInfo.realName || '未设置' }}</span>
              </div>
              <div class="info-item">
                <label>邮箱：</label>
                <span>{{ userInfo.email || '未设置' }}</span>
              </div>
              <div class="info-item">
                <label>手机号：</label>
                <span>{{ userInfo.phone || '未设置' }}</span>
              </div>
              <div class="info-item">
                <label>角色：</label>
                <el-tag :type="getRoleType(userInfo.role)">{{ getRoleLabel(userInfo.role) }}</el-tag>
              </div>
              <div v-if="userInfo.role === 'STUDENT'" class="info-item">
                <label>年级：</label>
                <span>{{ getGradeLabel(userInfo.grade) || '未设置' }}</span>
              </div>
              <div v-if="userInfo.school" class="info-item">
                <label>学校：</label>
                <span>{{ userInfo.school }}</span>
              </div>
              <div v-if="userInfo.className" class="info-item">
                <label>班级：</label>
                <span>{{ userInfo.className }}</span>
              </div>
            </div>

            <!-- 账户状态 -->
            <div class="account-status">
              <div class="info-item">
                <label>账户状态：</label>
                <el-tag :type="userInfo.status === 'ACTIVE' ? 'success' : 'danger'">
                  {{ userInfo.status === 'ACTIVE' ? '正常' : '禁用' }}
                </el-tag>
              </div>
              <div class="info-item">
                <label>注册时间：</label>
                <span>{{ formatDate(userInfo.createTime) }}</span>
              </div>
              <div v-if="userInfo.lastLoginTime" class="info-item">
                <label>最后登录：</label>
                <span>{{ formatDate(userInfo.lastLoginTime) }}</span>
              </div>
            </div>
          </div>
        </el-card>
      </el-col>

      <!-- 右侧功能区域 -->
      <el-col :span="16">
        <el-tabs v-model="activeTab" class="profile-tabs">
          <!-- 编辑资料 -->
          <el-tab-pane label="编辑资料" name="edit">
            <el-card>
              <el-form ref="profileFormRef" :model="profileForm" :rules="profileRules" label-width="100px"
                class="profile-form">
                <el-form-item label="真实姓名" prop="realName">
                  <el-input v-model="profileForm.realName" placeholder="请输入真实姓名" />
                </el-form-item>

                <el-form-item label="邮箱" prop="email">
                  <el-input v-model="profileForm.email" placeholder="请输入邮箱地址" />
                </el-form-item>

                <el-form-item label="手机号" prop="phone">
                  <el-input v-model="profileForm.phone" placeholder="请输入手机号" />
                </el-form-item>

                <el-form-item v-if="userInfo.role === 'STUDENT'" label="年级" prop="grade">
                  <el-select v-model="profileForm.grade" placeholder="请选择年级" style="width: 100%">
                    <el-option label="一年级" value="GRADE_1" />
                    <el-option label="二年级" value="GRADE_2" />
                    <el-option label="三年级" value="GRADE_3" />
                    <el-option label="四年级" value="GRADE_4" />
                    <el-option label="五年级" value="GRADE_5" />
                    <el-option label="六年级" value="GRADE_6" />
                    <el-option label="七年级" value="GRADE_7" />
                    <el-option label="八年级" value="GRADE_8" />
                    <el-option label="九年级" value="GRADE_9" />
                    <el-option label="高一" value="GRADE_10" />
                    <el-option label="高二" value="GRADE_11" />
                    <el-option label="高三" value="GRADE_12" />
                  </el-select>
                </el-form-item>

                <el-form-item label="学校" prop="school">
                  <el-input v-model="profileForm.school" placeholder="请输入学校名称" />
                </el-form-item>

                <el-form-item v-if="userInfo.role === 'STUDENT'" label="班级" prop="className">
                  <el-input v-model="profileForm.className" placeholder="请输入班级" />
                </el-form-item>

                <el-form-item>
                  <el-button type="primary" @click="updateProfile" :loading="updating">
                    保存修改
                  </el-button>
                  <el-button @click="resetForm">重置</el-button>
                </el-form-item>
              </el-form>
            </el-card>
          </el-tab-pane>

          <!-- 修改密码 -->
          <el-tab-pane label="修改密码" name="password">
            <el-card>
              <el-form ref="passwordFormRef" :model="passwordForm" :rules="passwordRules" label-width="100px"
                class="password-form">
                <el-form-item label="当前密码" prop="currentPassword">
                  <el-input v-model="passwordForm.currentPassword" type="password" placeholder="请输入当前密码"
                    show-password />
                </el-form-item>

                <el-form-item label="新密码" prop="newPassword">
                  <el-input v-model="passwordForm.newPassword" type="password" placeholder="请输入新密码" show-password />
                </el-form-item>

                <el-form-item label="确认密码" prop="confirmPassword">
                  <el-input v-model="passwordForm.confirmPassword" type="password" placeholder="请再次输入新密码"
                    show-password />
                </el-form-item>

                <el-form-item>
                  <el-button type="primary" @click="changePassword" :loading="changingPassword">
                    修改密码
                  </el-button>
                  <el-button @click="resetPasswordForm">重置</el-button>
                </el-form-item>
              </el-form>
            </el-card>
          </el-tab-pane>

          <!-- 账户统计 -->
          <el-tab-pane label="使用统计" name="stats">
            <el-card>
              <div class="stats-container">
                <el-row :gutter="16">
                  <el-col :span="8">
                    <div class="stat-item">
                      <div class="stat-number">{{ stats.analysisCount || 0 }}</div>
                      <div class="stat-label">文本分析次数</div>
                    </div>
                  </el-col>
                  <el-col :span="8">
                    <div class="stat-item">
                      <div class="stat-number">{{ stats.simplificationCount || 0 }}</div>
                      <div class="stat-label">文本简化次数</div>
                    </div>
                  </el-col>
                  <el-col v-if="userInfo.role === 'TEACHER'" :span="8">
                    <div class="stat-item">
                      <div class="stat-number">{{ stats.resourceCount || 0 }}</div>
                      <div class="stat-label">生成教学资源</div>
                    </div>
                  </el-col>
                </el-row>

                <el-divider />

                <div class="recent-activity">
                  <h3>最近活动</h3>
                  <el-timeline>
                    <el-timeline-item v-for="activity in recentActivities" :key="activity.id"
                      :timestamp="formatDate(activity.createTime)">
                      {{ activity.description }}
                    </el-timeline-item>
                  </el-timeline>
                </div>
              </div>
            </el-card>
          </el-tab-pane>
        </el-tabs>
      </el-col>
    </el-row>

    <!-- 头像上传对话框 -->
    <el-dialog v-model="showAvatarUpload" title="更换头像" width="400px">
      <div class="avatar-upload">
        <el-upload class="avatar-uploader" action="#" :show-file-list="false" :before-upload="beforeAvatarUpload"
          :http-request="uploadAvatar">
          <img v-if="newAvatarUrl" :src="newAvatarUrl" class="avatar-preview" />
          <el-icon v-else class="avatar-uploader-icon">
            <Plus />
          </el-icon>
        </el-upload>
        <div class="upload-tips">
          <p>支持 JPG、PNG 格式，文件大小不超过 2MB</p>
        </div>
      </div>
      <template #footer>
        <el-button @click="showAvatarUpload = false">取消</el-button>
        <el-button type="primary" @click="confirmAvatarUpload" :loading="uploadingAvatar">
          确定
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import { getUserInfo as getApiUserInfo, changePassword, updateUserProfile, uploadAvatar } from '@/api/auth'
import { getUserInfo, setUserInfo } from '@/utils/auth'
import { ElMessage } from 'element-plus'
import { Plus } from '@element-plus/icons-vue'

export default {
  name: 'UserProfile',
  components: {
    Plus
  },
  data() {
    const validateConfirmPassword = (rule, value, callback) => {
      if (value !== this.passwordForm.newPassword) {
        callback(new Error('两次输入密码不一致'))
      } else {
        callback()
      }
    }

    return {
      activeTab: 'edit',
      updating: false,
      changingPassword: false,
      uploadingAvatar: false,
      showAvatarUpload: false,
      newAvatarUrl: '',
      avatarFile: null,
      userInfo: {},
      profileForm: {
        realName: '',
        email: '',
        phone: '',
        grade: '',
        school: '',
        className: ''
      },
      profileRules: {
        realName: [
          { required: true, message: '请输入真实姓名', trigger: 'blur' },
          { min: 2, max: 20, message: '姓名长度在 2 到 20 个字符', trigger: 'blur' }
        ],
        email: [
          { required: true, message: '请输入邮箱地址', trigger: 'blur' },
          { type: 'email', message: '请输入正确的邮箱地址', trigger: 'blur' }
        ],
        phone: [
          { pattern: /^1[3-9]\d{9}$/, message: '手机号格式不正确', trigger: 'blur' }
        ]
      },
      passwordForm: {
        currentPassword: '',
        newPassword: '',
        confirmPassword: ''
      },
      passwordRules: {
        currentPassword: [
          { required: true, message: '请输入当前密码', trigger: 'blur' }
        ],
        newPassword: [
          { required: true, message: '请输入新密码', trigger: 'blur' },
          { min: 6, max: 20, message: '密码长度在 6 到 20 个字符', trigger: 'blur' }
        ],
        confirmPassword: [
          { required: true, message: '请确认新密码', trigger: 'blur' },
          { validator: validateConfirmPassword, trigger: 'blur' }
        ]
      },
      stats: {
        analysisCount: 0,
        simplificationCount: 0,
        resourceCount: 0
      },
      recentActivities: []
    }
  },
  async mounted() {
    await this.loadUserInfo()
    this.initProfileForm()
    // TODO: 加载使用统计和最近活动
  },
  methods: {
    async loadUserInfo() {
      try {
        // 先从本地获取用户信息
        this.userInfo = getUserInfo()

        // 如果需要最新信息，可以调用API
        // const response = await getApiUserInfo()
        // this.userInfo = response
        // setUserInfo(response)
      } catch (error) {
        console.error('加载用户信息失败:', error)
        ElMessage.error('加载用户信息失败')
      }
    },

    initProfileForm() {
      this.profileForm = {
        realName: this.userInfo.realName || '',
        email: this.userInfo.email || '',
        phone: this.userInfo.phone || '',
        grade: this.userInfo.grade || '',
        school: this.userInfo.school || '',
        className: this.userInfo.className || ''
      }
    },

    async updateProfile() {
      try {
        const valid = await this.$refs.profileFormRef.validate()
        if (!valid) return

        this.updating = true

        // 调用更新用户信息API
        await updateUserProfile(this.profileForm)

        // 更新本地存储
        const updatedUserInfo = { ...this.userInfo, ...this.profileForm }
        setUserInfo(updatedUserInfo)
        this.userInfo = updatedUserInfo

        ElMessage.success('个人信息更新成功')
      } catch (error) {
        console.error('更新个人信息失败:', error)
        ElMessage.error(error.message || '更新个人信息失败')
      } finally {
        this.updating = false
      }
    },

    resetForm() {
      this.initProfileForm()
      this.$refs.profileFormRef?.clearValidate()
    },

    async changePassword() {
      try {
        const valid = await this.$refs.passwordFormRef.validate()
        if (!valid) return

        this.changingPassword = true

        await changePassword({
          currentPassword: this.passwordForm.currentPassword,
          newPassword: this.passwordForm.newPassword
        })

        ElMessage.success('密码修改成功')
        this.resetPasswordForm()
      } catch (error) {
        console.error('修改密码失败:', error)
        ElMessage.error(error.message || '修改密码失败')
      } finally {
        this.changingPassword = false
      }
    },

    resetPasswordForm() {
      this.passwordForm = {
        currentPassword: '',
        newPassword: '',
        confirmPassword: ''
      }
      this.$refs.passwordFormRef?.clearValidate()
    },

    beforeAvatarUpload(file) {
      const isJPG = file.type === 'image/jpeg' || file.type === 'image/png'
      const isLt2M = file.size / 1024 / 1024 < 2

      if (!isJPG) {
        ElMessage.error('头像图片只能是 JPG/PNG 格式!')
        return false
      }
      if (!isLt2M) {
        ElMessage.error('头像图片大小不能超过 2MB!')
        return false
      }
      return true
    },

    uploadAvatar(options) {
      // 预览图片
      const reader = new FileReader()
      reader.onload = (e) => {
        this.newAvatarUrl = e.target.result
      }
      reader.readAsDataURL(options.file)

      // 保存文件对象供后续上传使用
      this.avatarFile = options.file
    },

    async confirmAvatarUpload() {
      if (!this.newAvatarUrl) {
        ElMessage.warning('请先选择头像图片')
        return
      }

      try {
        this.uploadingAvatar = true

        // 创建FormData对象
        const formData = new FormData()
        formData.append('avatar', this.avatarFile)

        // 调用上传头像API
        const response = await uploadAvatar(formData)
        const avatarUrl = response.url || response.avatar

        // 更新用户信息
        const updatedUserInfo = { ...this.userInfo, avatar: avatarUrl }
        setUserInfo(updatedUserInfo)
        this.userInfo = updatedUserInfo

        ElMessage.success('头像更新成功')
        this.showAvatarUpload = false
        this.newAvatarUrl = ''
        this.avatarFile = null
      } catch (error) {
        console.error('上传头像失败:', error)
        ElMessage.error(error.message || '上传头像失败')
      } finally {
        this.uploadingAvatar = false
      }
    },

    getRoleType(role) {
      const types = {
        'STUDENT': 'primary',
        'TEACHER': 'success',
        'ADMIN': 'danger'
      }
      return types[role] || 'info'
    },

    getRoleLabel(role) {
      const labels = {
        'STUDENT': '学生',
        'TEACHER': '教师',
        'ADMIN': '管理员'
      }
      return labels[role] || role
    },

    getGradeLabel(grade) {
      const labels = {
        'GRADE_1': '一年级',
        'GRADE_2': '二年级',
        'GRADE_3': '三年级',
        'GRADE_4': '四年级',
        'GRADE_5': '五年级',
        'GRADE_6': '六年级',
        'GRADE_7': '七年级',
        'GRADE_8': '八年级',
        'GRADE_9': '九年级',
        'GRADE_10': '高一',
        'GRADE_11': '高二',
        'GRADE_12': '高三'
      }
      return labels[grade] || grade
    },

    formatDate(dateString) {
      if (!dateString) return '未知'
      const date = new Date(dateString)
      return date.toLocaleString('zh-CN')
    }
  }
}
</script>

<style lang="scss" scoped>
.profile-container {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.profile-header {
  margin-bottom: 24px;
  text-align: center;

  h1 {
    color: var(--el-text-color-primary);
    margin-bottom: 8px;
  }

  p {
    color: var(--el-text-color-regular);
    margin: 0;
  }
}

.profile-card {
  height: fit-content;

  .card-header {
    font-weight: 600;
    color: var(--el-text-color-primary);
  }
}

.profile-info {
  .avatar-section {
    text-align: center;
    margin-bottom: 24px;

    .user-avatar {
      background: var(--el-color-primary-light-8);
      color: var(--el-color-primary);
      font-size: 32px;
      font-weight: bold;
    }
  }

  .basic-info,
  .account-status {
    margin-bottom: 20px;
  }

  .info-item {
    display: flex;
    align-items: center;
    margin-bottom: 12px;

    label {
      min-width: 80px;
      color: var(--el-text-color-regular);
      font-size: 14px;
    }

    span {
      color: var(--el-text-color-primary);
      flex: 1;
    }
  }
}

.profile-tabs {
  :deep(.el-tabs__header) {
    margin-bottom: 20px;
  }
}

.profile-form,
.password-form {
  max-width: 600px;

  .el-form-item {
    margin-bottom: 20px;
  }
}

.stats-container {
  .stat-item {
    text-align: center;
    padding: 20px;
    border: 1px solid var(--el-border-color-light);
    border-radius: 8px;

    .stat-number {
      font-size: 32px;
      font-weight: bold;
      color: var(--el-color-primary);
      margin-bottom: 8px;
    }

    .stat-label {
      color: var(--el-text-color-regular);
      font-size: 14px;
    }
  }

  .recent-activity {
    margin-top: 24px;

    h3 {
      color: var(--el-text-color-primary);
      margin-bottom: 16px;
    }
  }
}

.avatar-upload {
  text-align: center;

  .avatar-uploader {
    :deep(.el-upload) {
      border: 1px dashed var(--el-border-color);
      border-radius: 6px;
      cursor: pointer;
      position: relative;
      overflow: hidden;
      transition: var(--el-transition-duration-fast);

      &:hover {
        border-color: var(--el-color-primary);
      }
    }
  }

  .avatar-uploader-icon {
    font-size: 28px;
    color: #8c939d;
    width: 120px;
    height: 120px;
    text-align: center;
    line-height: 120px;
  }

  .avatar-preview {
    width: 120px;
    height: 120px;
    display: block;
    object-fit: cover;
  }

  .upload-tips {
    margin-top: 12px;

    p {
      color: var(--el-text-color-regular);
      font-size: 12px;
      margin: 0;
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .profile-container {
    padding: 16px;
  }

  .el-col {
    margin-bottom: 16px;
  }

  .profile-form,
  .password-form {
    max-width: 100%;
  }
}
</style>
