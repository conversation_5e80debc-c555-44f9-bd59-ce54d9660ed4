import request from '@/utils/request'

/**
 * 文本分级分析
 * @param {Object} data - 分析数据
 * @param {string} data.text - 文本内容
 * @param {string} data.title - 文本标题
 * @param {string} data.source - 文本来源
 * @param {boolean} data.detailedAnalysis - 是否详细分析
 * @param {boolean} data.generateTeachingAdvice - 是否生成教学建议
 */
export function analyzeText(data) {
  return request({
    url: '/text-analysis/analyze',
    method: 'post',
    data,
    timeout: 60000 // 分析可能需要较长时间
  })
}

/**
 * 文件上传分析
 * @param {FormData} formData - 文件数据
 */
export function analyzeFile(formData) {
  return request({
    url: '/text-analysis/analyze-file',
    method: 'post',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data'
    },
    timeout: 60000
  })
}

/**
 * 批量文本分析
 * @param {Array} data - 文本数组
 */
export function batchAnalyze(data) {
  return request({
    url: '/text-analysis/batch-analyze',
    method: 'post',
    data,
    timeout: 120000
  })
}

/**
 * 获取分析历史
 * @param {Object} params - 查询参数
 * @param {number} params.page - 页码
 * @param {number} params.size - 每页大小
 * @param {string} params.gradeLevel - 分级筛选
 */
export function getAnalysisHistory(params) {
  return request({
    url: '/text-analysis/history',
    method: 'get',
    params
  })
}

/**
 * 获取分析详情
 * @param {number} analysisId - 分析ID
 */
export function getAnalysisDetail(analysisId) {
  return request({
    url: `/text-analysis/${analysisId}`,
    method: 'get'
  })
}

/**
 * 删除分析记录
 * @param {number} analysisId - 分析ID
 */
export function deleteAnalysis(analysisId) {
  return request({
    url: `/text-analysis/${analysisId}`,
    method: 'delete'
  })
}

/**
 * 获取分级标准
 */
export function getGradeStandards() {
  return request({
    url: '/text-analysis/grade-standards',
    method: 'get'
  })
}
