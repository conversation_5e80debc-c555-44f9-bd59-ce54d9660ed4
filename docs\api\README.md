# 智慧阅读系统 API 接口文档

## 概述

智慧阅读系统后端API提供文本分析、文本简化、教学工具等核心功能，支持AI驱动的文本难度分级与阅读辅助。

## 基础信息

- **基础URL**: `http://localhost:8080/api`
- **认证方式**: JWT Bearer Token
- **请求格式**: JSON
- **响应格式**: JSON
- **字符编码**: UTF-8
- **API版本**: v1.0.0

## 认证说明

除了公开接口外，所有API请求都需要在Header中携带JWT Token：

```
Authorization: Bearer <your-jwt-token>
```

## 通用响应格式

### 成功响应
```json
{
  "code": 200,
  "message": "success",
  "data": { ... }
}
```

### 错误响应
```json
{
  "code": 400,
  "message": "错误描述",
  "timestamp": "2024-01-01T12:00:00"
}
```

## 1. 用户认证模块

### 1.1 用户登录
- **URL**: `/auth/login`
- **方法**: `POST`
- **描述**: 用户名密码登录获取访问令牌
- **认证**: 无需认证

**请求参数**:
```json
{
  "username": "string",      // 用户名，3-20字符
  "password": "string",      // 密码，6-20字符
  "rememberMe": false,       // 记住我，可选
  "captcha": "string",       // 验证码，可选
  "captchaKey": "string"     // 验证码key，可选
}
```

**响应示例**:
```json
{
  "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "refreshToken": "refresh_token_string",
  "expiresIn": 86400,
  "userInfo": {
    "id": 1,
    "username": "teacher01",
    "email": "<EMAIL>",
    "role": "TEACHER",
    "realName": "张老师"
  }
}
```

### 1.2 用户注册
- **URL**: `/auth/register`
- **方法**: `POST`
- **描述**: 新用户注册
- **认证**: 无需认证

**请求参数**:
```json
{
  "username": "string",      // 用户名，3-20字符
  "password": "string",      // 密码，6-20字符
  "email": "string",         // 邮箱地址
  "realName": "string",      // 真实姓名
  "role": "STUDENT"          // 角色：STUDENT/TEACHER
}
```

### 1.3 用户登出
- **URL**: `/auth/logout`
- **方法**: `POST`
- **描述**: 用户登出，清除token
- **认证**: 需要认证

### 1.4 获取用户信息
- **URL**: `/auth/user-info`
- **方法**: `GET`
- **描述**: 获取当前登录用户信息
- **认证**: 需要认证

**响应示例**:
```json
{
  "id": 1,
  "username": "teacher01",
  "email": "<EMAIL>",
  "role": "TEACHER",
  "realName": "张老师",
  "avatar": "http://example.com/avatar.jpg",
  "createdAt": "2024-01-01T12:00:00"
}
```

### 1.5 刷新Token
- **URL**: `/auth/refresh-token`
- **方法**: `POST`
- **描述**: 使用刷新令牌获取新的访问令牌
- **认证**: 无需认证

**请求参数**:
```
refreshToken: string  // 查询参数
```

### 1.6 修改密码
- **URL**: `/auth/change-password`
- **方法**: `POST`
- **描述**: 修改当前用户密码
- **认证**: 需要认证

**请求参数**:
```json
{
  "currentPassword": "string",  // 当前密码
  "newPassword": "string"       // 新密码
}
```

### 1.7 忘记密码
- **URL**: `/auth/forgot-password`
- **方法**: `POST`
- **描述**: 发送重置密码邮件
- **认证**: 无需认证

**请求参数**:
```json
{
  "email": "string"  // 邮箱地址
}
```

### 1.8 重置密码
- **URL**: `/auth/reset-password`
- **方法**: `POST`
- **描述**: 通过重置令牌重置密码
- **认证**: 无需认证

**请求参数**:
```json
{
  "token": "string",      // 重置令牌
  "newPassword": "string" // 新密码
}
```

### 1.9 发送验证码
- **URL**: `/auth/send-verification-code`
- **方法**: `POST`
- **描述**: 发送邮箱或手机验证码
- **认证**: 无需认证

**请求参数**:
```json
{
  "type": "EMAIL",        // 类型：EMAIL/SMS
  "target": "string"      // 邮箱或手机号
}
```

### 1.10 验证邮箱
- **URL**: `/auth/verify-email`
- **方法**: `POST`
- **描述**: 验证用户邮箱
- **认证**: 需要认证

**请求参数**:
```json
{
  "email": "string",  // 邮箱地址
  "code": "string"    // 验证码
}
```

## 2. 文本分析模块

### 2.1 文本分级分析
- **URL**: `/text-analysis/analyze`
- **方法**: `POST`
- **描述**: 对输入的文本进行难度分级和详细分析
- **认证**: 需要认证

**请求参数**:
```json
{
  "text": "要分析的文本内容",           // 必填，10-50000字符
  "title": "文本标题",              // 可选，最大200字符
  "source": "文本来源",             // 可选，最大500字符
  "detailedAnalysis": true,         // 是否需要详细分析
  "generateTeachingAdvice": false   // 是否生成教学建议
}
```

**响应示例**:
```json
{
  "analysisId": 12345,
  "title": "示例文本",
  "source": "教材",
  "gradeResult": {
    "gradeCode": "PRIMARY_HIGH",
    "gradeName": "小学高年级",
    "gradeDescription": "5-6年级",
    "confidence": 0.85,
    "recommendedGrades": "五年级-六年级",
    "color": "#F56C6C"
  },
  "basicStats": {
    "textLength": 1200,
    "wordCount": 800,
    "sentenceCount": 45,
    "paragraphCount": 8,
    "avgSentenceLength": 17.8
  },
  "vocabularyAnalysis": {
    "newWordRate": 0.15,
    "newWords": [
      {
        "word": "蜿蜒",
        "partOfSpeech": "动词",
        "difficultyLevel": "高级",
        "frequency": 2,
        "suggestedReplacement": "弯曲"
      }
    ],
    "keywords": ["自然", "山川", "河流"],
    "distribution": {
      "basicWordsRate": 0.70,
      "commonWordsRate": 0.15,
      "advancedWordsRate": 0.15
    }
  },
  "sentenceAnalysis": {
    "complexSentenceRate": 0.25,
    "longSentences": [
      {
        "content": "这是一个很长的句子...",
        "length": 45,
        "complexityScore": 0.8,
        "simplificationSuggestion": "建议拆分为两个句子"
      }
    ],
    "distribution": {
      "simpleSentenceRate": 0.60,
      "compoundSentenceRate": 0.15,
      "complexSentenceRate": 0.25
    }
  },
  "topicAnalysis": {
    "category": "自然",
    "topicKeywords": ["山川", "河流", "自然"],
    "sentiment": "中性",
    "textType": "说明文"
  },
  "teachingAdvice": {
    "teachingFocus": ["词汇理解", "句式分析"],
    "preparationAdvice": ["预习生词", "了解背景"],
    "readingStrategies": ["分段阅读", "关键词标记"],
    "practiceAdvice": ["词汇练习", "句式仿写"]
  },
  "analysisTime": "2024-01-15T10:30:00"
}
```

### 2.2 文件上传分析
- **URL**: `/text-analysis/analyze-file`
- **方法**: `POST`
- **描述**: 上传文件进行文本分析
- **认证**: 需要认证

**请求参数**:
- `file`: 文本文件（支持txt、doc、docx、pdf，最大10MB）
- `title`: 文件标题（可选）
- `detailedAnalysis`: 是否详细分析（默认true）

### 2.3 批量文本分析
- **URL**: `/text-analysis/batch-analyze`
- **方法**: `POST`
- **描述**: 批量分析多个文本
- **认证**: 需要认证

**请求参数**:
```json
[
  {
    "text": "文本内容1",
    "title": "标题1"
  },
  {
    "text": "文本内容2",
    "title": "标题2"
  }
]
```

### 2.4 获取分析历史
- **URL**: `/text-analysis/history`
- **方法**: `GET`
- **描述**: 获取用户的文本分析历史记录
- **认证**: 需要认证

**请求参数**:
- `page`: 页码（默认1）
- `size`: 每页大小（默认10）
- `gradeLevel`: 分级筛选（可选）

### 2.5 获取分析详情
- **URL**: `/text-analysis/{analysisId}`
- **方法**: `GET`
- **描述**: 根据分析ID获取详细分析结果
- **认证**: 需要认证

### 2.6 删除分析记录
- **URL**: `/text-analysis/{analysisId}`
- **方法**: `DELETE`
- **描述**: 删除指定的分析记录
- **认证**: 需要认证

### 2.7 获取分级标准
- **URL**: `/text-analysis/grade-standards`
- **方法**: `GET`
- **描述**: 获取系统支持的分级标准信息
- **认证**: 无需认证

**响应示例**:
```json
[
  {
    "code": "PRIMARY_LOW",
    "name": "小学低年级",
    "description": "1-2年级",
    "color": "#67C23A",
    "minAge": 6,
    "maxAge": 8
  },
  {
    "code": "PRIMARY_MID",
    "name": "小学中年级",
    "description": "3-4年级",
    "color": "#E6A23C",
    "minAge": 8,
    "maxAge": 10
  }
]
```

## 3. 文本简化模块

### 3.1 文本简化
- **URL**: `/text-simplification/simplify`
- **方法**: `POST`
- **描述**: 将文本简化到指定年级水平
- **认证**: 需要认证

**请求参数**:
```json
{
  "text": "要简化的文本内容",           // 必填，原始文本
  "targetGrade": "PRIMARY_MID",        // 目标年级
  "analysisId": 123,                   // 可选，基于已有分析
  "simplificationLevel": "MODERATE",   // 简化程度：LIGHT/MODERATE/HEAVY
  "preserveStructure": true,           // 是否保持结构
  "generateComparison": true,          // 是否生成对比
  "specialRequirements": "保持故事情节完整"  // 特殊要求
}
```

**响应示例**:
```json
{
  "simplificationId": 67890,
  "originalText": "原始文本内容",
  "simplifiedText": "简化后的文本内容",
  "targetGrade": "PRIMARY_MID",
  "originalGrade": {
    "gradeCode": "PRIMARY_HIGH",
    "gradeName": "小学高年级",
    "confidence": 0.85
  },
  "simplifiedGrade": {
    "gradeCode": "PRIMARY_MID",
    "gradeName": "小学中年级",
    "confidence": 0.90
  },
  "statistics": {
    "originalWordCount": 800,
    "simplifiedWordCount": 650,
    "wordCountChangeRate": -0.1875,
    "simplificationRatio": 0.75
  },
  "wordReplacements": [
    {
      "originalWord": "蜿蜒",
      "replacementWord": "弯曲",
      "reason": "降低词汇难度",
      "positions": [45, 123],
      "difficultyReduction": "高级->基础"
    }
  ],
  "sentenceAdjustments": [
    {
      "originalSentence": "原始句子",
      "adjustedSentence": "调整后句子",
      "adjustmentType": "SPLIT",
      "reason": "句子过长"
    }
  ],
  "qualityAssessment": {
    "overallScore": 85.5,
    "readabilityImprovement": 78.0,
    "contentFidelity": 92.0,
    "languageFluency": 87.0
  },
  "simplificationTime": "2024-01-15T10:35:00"
}
```

### 3.2 基于分析结果简化
- **URL**: `/text-simplification/simplify-from-analysis`
- **方法**: `POST`
- **描述**: 基于已有的分析结果进行文本简化
- **认证**: 需要认证

**请求参数**:
- `analysisId`: 分析ID
- `targetGrade`: 目标年级
- `simplificationLevel`: 简化程度（默认MODERATE）

### 3.3 批量文本简化
- **URL**: `/text-simplification/batch-simplify`
- **方法**: `POST`
- **描述**: 批量简化多个文本
- **认证**: 需要认证

### 3.4 获取简化历史
- **URL**: `/text-simplification/history`
- **方法**: `GET`
- **描述**: 获取用户的文本简化历史记录
- **认证**: 需要认证

**请求参数**:
- `page`: 页码（默认1）
- `size`: 每页大小（默认10）
- `targetGrade`: 目标年级筛选（可选）

### 3.5 获取简化详情
- **URL**: `/text-simplification/{simplificationId}`
- **方法**: `GET`
- **描述**: 根据简化ID获取详细简化结果
- **认证**: 需要认证

### 3.6 删除简化记录
- **URL**: `/text-simplification/{simplificationId}`
- **方法**: `DELETE`
- **描述**: 删除指定的简化记录
- **认证**: 需要认证

### 3.7 简化效果评估
- **URL**: `/text-simplification/{simplificationId}/evaluate`
- **方法**: `POST`
- **描述**: 评估简化文本的质量和效果
- **认证**: 需要认证

## 4. 教学工具模块

### 4.1 生成教学资源
- **URL**: `/teaching-tools/generate-resources`
- **方法**: `POST`
- **描述**: 根据文本分析结果生成教学资源
- **认证**: 需要认证（教师或管理员权限）

**请求参数**:
```json
{
  "analysisId": 12345,                    // 分析ID
  "resourceTypes": [                      // 资源类型数组
    "VOCABULARY_CARDS",                   // 生词卡片
    "COMPREHENSION_QUESTIONS",            // 理解题目
    "READING_GUIDE"                       // 阅读指导
  ],
  "gradeLevel": "PRIMARY_HIGH",           // 年级水平
  "studentLevel": "BEGINNER",             // 学生水平
  "customRequirements": "重点关注词汇理解"  // 自定义要求
}
```

**响应示例**:
```json
{
  "resourceId": 98765,
  "analysisId": 12345,
  "resourceTypes": ["VOCABULARY_CARDS", "COMPREHENSION_QUESTIONS"],
  "vocabularyCards": [
    {
      "word": "蜿蜒",
      "pronunciation": "wān yán",
      "definition": "弯弯曲曲地延伸",
      "example": "小河蜿蜒流过山谷",
      "difficulty": "高级",
      "imageUrl": "http://example.com/image.jpg"
    }
  ],
  "comprehensionQuestions": [
    {
      "question": "文中描述的景色有什么特点？",
      "type": "OPEN_ENDED",
      "difficulty": "MEDIUM",
      "suggestedAnswer": "景色优美，山川蜿蜒..."
    }
  ],
  "readingGuide": {
    "preReadingActivities": ["预习生词", "了解背景"],
    "readingStrategies": ["分段阅读", "关键词标记"],
    "postReadingActivities": ["讨论问题", "写作练习"]
  },
  "createdTime": "2024-01-15T11:00:00"
}
```

### 4.2 获取推荐文本
- **URL**: `/teaching-tools/recommend-texts`
- **方法**: `POST`
- **描述**: 根据条件推荐合适的阅读文本
- **认证**: 需要认证

**请求参数**:
```json
{
  "gradeLevel": "PRIMARY_HIGH",     // 年级水平
  "category": "自然",               // 文本分类（可选）
  "textType": "说明文",             // 文体类型（可选）
  "count": 10,                      // 推荐数量（默认10）
  "keywords": ["山川", "河流"],     // 关键词（可选）
  "excludeAnalysisIds": [123, 456]  // 排除的分析ID（可选）
}
```

### 4.3 获取同级文本
- **URL**: `/teaching-tools/similar-texts`
- **方法**: `GET`
- **描述**: 根据分级结果获取同等难度的文本
- **认证**: 需要认证

**请求参数**:
- `analysisId`: 分析ID
- `count`: 推荐数量（默认10）

### 4.4 生成生词卡片
- **URL**: `/teaching-tools/vocabulary-cards`
- **方法**: `POST`
- **描述**: 根据文本生成生词学习卡片
- **认证**: 需要认证

**请求参数**:
- `analysisId`: 分析ID
- `targetGrade`: 目标年级（可选）

### 4.5 生成阅读理解题
- **URL**: `/teaching-tools/comprehension-questions`
- **方法**: `POST`
- **描述**: 根据文本生成阅读理解练习题
- **认证**: 需要认证

**请求参数**:
- `analysisId`: 分析ID
- `questionCount`: 题目数量（默认5）
- `difficulty`: 难度等级（默认MEDIUM）

### 4.6 生成阅读指导
- **URL**: `/teaching-tools/reading-guide`
- **方法**: `POST`
- **描述**: 根据文本生成阅读指导建议
- **认证**: 需要认证

**请求参数**:
- `analysisId`: 分析ID
- `studentLevel`: 学生水平（默认BEGINNER）

### 4.7 获取教学资源历史
- **URL**: `/teaching-tools/resources/history`
- **方法**: `GET`
- **描述**: 获取用户的教学资源生成历史
- **认证**: 需要认证（教师或管理员权限）

**请求参数**:
- `page`: 页码（默认1）
- `size`: 每页大小（默认10）
- `resourceType`: 资源类型（可选）

### 4.8 导出教学资源
- **URL**: `/teaching-tools/resources/{resourceId}/export`
- **方法**: `GET`
- **描述**: 导出教学资源为文档
- **认证**: 需要认证（教师或管理员权限）

**请求参数**:
- `format`: 导出格式（默认PDF）

### 4.9 分享教学资源
- **URL**: `/teaching-tools/resources/{resourceId}/share`
- **方法**: `POST`
- **描述**: 分享教学资源给其他用户
- **认证**: 需要认证（教师或管理员权限）

**请求参数**:
```json
{
  "userIds": [1, 2, 3],        // 分享给的用户ID列表
  "message": "分享消息",        // 分享消息
  "allowEdit": false           // 是否允许编辑
}
```

### 4.10 评价教学资源
- **URL**: `/teaching-tools/resources/{resourceId}/rate`
- **方法**: `POST`
- **描述**: 对教学资源进行评价
- **认证**: 需要认证

**请求参数**:
```json
{
  "rating": 5,           // 评分（1-5）
  "comment": "很有用的资源"  // 评价内容
}
```

## 5. 系统配置

### 5.1 分级标准配置

系统支持以下分级标准：

| 分级代码 | 分级名称 | 描述 | 颜色 | 年龄范围 |
|---------|---------|------|------|---------|
| PRIMARY_LOW | 小学低年级 | 1-2年级 | #67C23A | 6-8岁 |
| PRIMARY_MID | 小学中年级 | 3-4年级 | #E6A23C | 8-10岁 |
| PRIMARY_HIGH | 小学高年级 | 5-6年级 | #F56C6C | 10-12岁 |
| JUNIOR_HIGH | 初中阶段 | 7-9年级 | #909399 | 12-15岁 |
| SENIOR_HIGH | 高中阶段 | 10-12年级 | #606266 | 15-18岁 |

### 5.2 AI服务配置

- **文本分析服务**: `http://localhost:8081/ai/text-analysis`
- **文本简化服务**: `http://localhost:8081/ai/text-simplify`
- **文本推荐服务**: `http://localhost:8081/ai/text-recommend`
- **服务超时**: 30秒

### 5.3 文件上传限制

- **最大文件大小**: 10MB
- **支持格式**: txt, doc, docx, pdf
- **上传路径**: `./uploads/`

## 6. 错误码说明

### 6.1 HTTP状态码

| 状态码 | 说明 |
|--------|------|
| 200 | 成功 |
| 400 | 请求参数错误 |
| 401 | 未授权 |
| 403 | 权限不足 |
| 404 | 资源不存在 |
| 500 | 服务器内部错误 |

### 6.2 业务错误码

| 错误码 | 说明 |
|--------|------|
| 1001 | 文本分析失败 |
| 1002 | 文本简化失败 |
| 1003 | AI服务不可用 |
| 1004 | 文件格式不支持 |
| 1005 | 文件大小超限 |
| 2001 | 用户不存在 |
| 2002 | 密码错误 |
| 2003 | 用户已存在 |
| 2004 | 邮箱已被使用 |
| 2005 | 验证码错误 |
| 3001 | 教学资源生成失败 |
| 3002 | 权限不足 |

## 7. 接口调用示例

### 7.1 JavaScript/Axios 示例

```javascript
// 设置基础配置
const api = axios.create({
  baseURL: 'http://localhost:8080/api',
  timeout: 30000
});

// 请求拦截器 - 添加token
api.interceptors.request.use(config => {
  const token = localStorage.getItem('token');
  if (token) {
    config.headers.Authorization = `Bearer ${token}`;
  }
  return config;
});

// 用户登录
const login = async (username, password) => {
  try {
    const response = await api.post('/auth/login', {
      username,
      password
    });
    const { token } = response.data;
    localStorage.setItem('token', token);
    return response.data;
  } catch (error) {
    console.error('登录失败:', error);
    throw error;
  }
};

// 文本分析
const analyzeText = async (text, title = '') => {
  try {
    const response = await api.post('/text-analysis/analyze', {
      text,
      title,
      detailedAnalysis: true,
      generateTeachingAdvice: false
    });
    return response.data;
  } catch (error) {
    console.error('分析失败:', error);
    throw error;
  }
};

// 文本简化
const simplifyText = async (text, targetGrade) => {
  try {
    const response = await api.post('/text-simplification/simplify', {
      text,
      targetGrade,
      simplificationLevel: 'MODERATE',
      preserveStructure: true,
      generateComparison: true
    });
    return response.data;
  } catch (error) {
    console.error('简化失败:', error);
    throw error;
  }
};

// 生成教学资源
const generateTeachingResources = async (analysisId, resourceTypes) => {
  try {
    const response = await api.post('/teaching-tools/generate-resources', {
      analysisId,
      resourceTypes,
      gradeLevel: 'PRIMARY_HIGH',
      studentLevel: 'BEGINNER'
    });
    return response.data;
  } catch (error) {
    console.error('生成教学资源失败:', error);
    throw error;
  }
};
```

### 7.2 Python/Requests 示例

```python
import requests
import json

class WisdomReadingAPI:
    def __init__(self, base_url='http://localhost:8080/api'):
        self.base_url = base_url
        self.token = None
        self.session = requests.Session()

    def login(self, username, password):
        """用户登录"""
        url = f"{self.base_url}/auth/login"
        data = {
            "username": username,
            "password": password
        }
        response = self.session.post(url, json=data)
        if response.status_code == 200:
            result = response.json()
            self.token = result['token']
            self.session.headers.update({
                'Authorization': f'Bearer {self.token}'
            })
            return result
        else:
            raise Exception(f"登录失败: {response.text}")

    def analyze_text(self, text, title='', detailed_analysis=True):
        """文本分析"""
        url = f"{self.base_url}/text-analysis/analyze"
        data = {
            "text": text,
            "title": title,
            "detailedAnalysis": detailed_analysis,
            "generateTeachingAdvice": False
        }
        response = self.session.post(url, json=data)
        if response.status_code == 200:
            return response.json()
        else:
            raise Exception(f"分析失败: {response.text}")

    def simplify_text(self, text, target_grade, simplification_level='MODERATE'):
        """文本简化"""
        url = f"{self.base_url}/text-simplification/simplify"
        data = {
            "text": text,
            "targetGrade": target_grade,
            "simplificationLevel": simplification_level,
            "preserveStructure": True,
            "generateComparison": True
        }
        response = self.session.post(url, json=data)
        if response.status_code == 200:
            return response.json()
        else:
            raise Exception(f"简化失败: {response.text}")

# 使用示例
api = WisdomReadingAPI()
api.login('teacher01', 'password123')

# 分析文本
result = api.analyze_text('这是一段需要分析的文本内容...', '示例文本')
print(f"分析结果: {result['gradeResult']['gradeName']}")

# 简化文本
simplified = api.simplify_text('复杂的文本内容...', 'PRIMARY_MID')
print(f"简化后: {simplified['simplifiedText']}")
```

## 8. 注意事项

### 8.1 请求限制
- 文本长度限制：10-50,000字符
- 文件上传大小限制：10MB
- 支持的文件格式：txt, doc, docx, pdf
- API调用频率限制：每分钟100次

### 8.2 认证相关
- JWT Token有效期：24小时
- 刷新Token有效期：7天
- Token需要在请求头中携带：`Authorization: Bearer <token>`

### 8.3 数据格式
- 所有时间字段均为ISO 8601格式
- 请求和响应均为JSON格式
- 字符编码：UTF-8

### 8.4 权限说明
- 学生（STUDENT）：可以使用文本分析、简化功能
- 教师（TEACHER）：可以使用所有功能，包括教学工具
- 管理员（ADMIN）：拥有所有权限

### 8.5 API文档
- Swagger UI: `http://localhost:8080/api/swagger-ui.html`
- API文档: `http://localhost:8080/api/v3/api-docs`

## 9. 更新日志

### v1.0.0 (2024-01-15)
- 初始版本发布
- 实现用户认证模块
- 实现文本分析功能
- 实现文本简化功能
- 实现教学工具功能
- 支持文件上传分析
- 支持批量处理
- 集成AI分析服务

---

**联系方式**
- 开发团队：Wisdom Reading Team
- 技术支持：<EMAIL>
- 文档版本：v1.0.0
- 最后更新：2024-01-15
